feature_id,feature_description,module_component,development_start_date,development_end_date,total_dev_days,lines_added,lines_deleted,lines_modified,files_changed,commits_count,complexity_score,team_size,developer_names,branch_name,is_jira_tracked,feature_type
ONE-1,- <PERSON>ra automation Testing. (pull request 637),source/Modules,2024-09-05,2024-09-05,1,20,20,40,1,1,1.2,1,<PERSON><PERSON>,Unknown,True,other
ONE-1081,- Add missing biometry validation on the transfer to lifemiles process (pull request 677),source/Modules,2024-10-10,2024-10-10,1,71,24,95,3,1,2.75,1,<PERSON>,Unknown,True,feature
ONE-1082,- Update String Copies for short capture (pull request 675),source/Modules,2024-10-08,2024-10-08,1,66,15,81,16,1,9.11,1,<PERSON>,Unknown,True,maintenance
ONE-1097,- Update String Copies for long capture (pull request 676),source/Modules,2024-10-11,2024-10-11,1,69,12,81,4,1,3.11,1,<PERSON>,Unknown,True,maintenance
ONE-1129,- Add localization string for scan tutorials (pull request 706),source/Modules,2024-11-08,2024-11-08,1,166,16,182,5,1,4.62,1,<PERSON> <PERSON>,Unknown,True,feature
ONE-1130,- <PERSON>d Appsflyer Events (pull request 709),source/<PERSON><PERSON><PERSON>,2024-11-11,2024-11-11,1,885,159,1044,35,1,10.0,1,<PERSON> <PERSON> V<PERSON><PERSON> <PERSON>,Unknown,True,testing
ONE-1132,- <PERSON><PERSON> educational level missing validation. (pull request 686),source/Modules,2024-10-22,2024-10-22,1,320,311,631,16,1,10.0,1,Emely Melgar,Unknown,True,testing
ONE-1133,bugfix-DataSaved (pull request 688),source/App Environment,2024-10-22,2024-10-23,2,201,138,339,5,2,6.49,1,Edgar Emilio Vásquez Castillo,Unknown,True,testing
ONE-1138,- Fix lifemiles points transfer ticker button configuration (pull request 710),source/Modules,2024-11-08,2024-11-08,1,9,4,13,1,1,0.93,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-1139,- Set correct rejection screens on biometry step (OB) (pull request 997),source/Modules,2025-06-18,2025-06-18,1,11,94,105,4,1,3.35,1,Rodrigo Mejia,Unknown,True,other
ONE-1229,Merged in bugfix(pull request 695),source/Modules,2024-10-30,2024-10-30,1,4,6,10,2,1,1.4,1,Vladimir Guevara,Unknown,True,bugfix
ONE-1235,- Add toolbar configuration for main navigation,source/DesignSystem,2024-11-29,2024-11-29,1,597,4,601,34,1,10.0,1,Vladimir Guevara,Unknown,True,testing
ONE-1241,- Improve validateUser call for password reset flows (pull request 700),source/Core,2024-10-30,2024-10-30,1,51,27,78,4,1,3.08,1,Rodrigo Mejia,Unknown,True,other
ONE-1242,- Remove dashes in the phone number value. (pull request 703),source/Modules,2024-11-05,2024-11-05,1,26,16,42,15,1,8.22,1,Emely Melgar,Unknown,True,other
ONE-1247,- Update URL Api request and rendering in view for display markdown text in Terms and Conditions section (pull request 802),source/Modules,2025-01-23,2025-01-23,1,19,5,24,4,1,2.54,1,Julio Rico,Unknown,True,testing
ONE-1252,- Update fastlane version to 2.225.0 to fix CI error (pull request 699),other,2024-10-30,2024-10-30,1,1,1,2,1,1,0.82,1,Emely Melgar,Unknown,True,bugfix
ONE-1259,- Revert CreditCardOfferCoordinator Creation in SplashRoute,source/Modules,2024-11-04,2024-11-04,1,2,2,4,1,1,0.84,1,Vladimir Guevara,Unknown,True,other
ONE-1260,- Fix typo and add new localizations to Profile contacts info flow (pull request 704),source/Modules,2024-11-05,2024-11-05,1,42,2,44,3,1,2.24,1,Rodrigo Mejia,Unknown,True,feature
ONE-1265,- Fix Labels of inputs in Profile references flow (pull request 705),source/Modules,2024-11-06,2024-11-06,1,112,13,125,4,1,3.55,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-1268,- Update transactions name in loyalty section (pull request 734),source/Modules,2024-12-02,2024-12-02,1,106,10,116,6,1,4.46,1,Julio Rico,Unknown,True,maintenance
ONE-1275,- add new user banner content in LoginPage,source/DesignSystem,2025-01-23,2025-01-23,1,298,102,400,21,1,10.0,1,Vladimir Guevara,Unknown,True,feature
ONE-1277,- Fix for Biometric Sheet for Touch ID and Face ID (pull request 707),source/Modules,2024-11-11,2024-11-11,1,209,36,245,8,1,6.75,1,Julio Rico,Unknown,True,testing
ONE-1278,- Fix UI and add new labels for document input in password and user recovery flow (pull request 722),source/Components,2024-11-22,2024-11-22,1,171,136,307,3,1,4.87,1,Rodrigo Mejia,Unknown,True,feature
ONE-1284,- iOS Update endpoint model for contact client and Account Beneficiaries (pull request 717),source/Modules,2024-11-22,2024-11-22,1,3117,454,3571,104,1,10.0,1,Julio Rico,Unknown,True,testing
ONE-1288,- Include the Dynatraces custom actions. (pull request 728),source/Modules,2024-11-28,2024-11-28,1,1710,392,2102,115,1,10.0,1,Emely Melgar,Unknown,True,testing
ONE-1297,- Set location marker when is coming back from the next view,source/Modules,2024-11-14,2024-11-14,1,8,5,13,1,1,0.93,1,Vladimir Guevara,Unknown,True,other
ONE-1299,- Cherry Pick - Add Analytic flag for remote configure (pull request 731),ios_code,2024-11-27,2024-11-27,1,20,13,33,8,1,4.63,1,Emely Melgar,Unknown,True,feature
ONE-1303,"- Add new OTP UI and logic for sms, WhatsApp and email codes (pull request 753)",source/OTP,2024-12-26,2024-12-26,1,3782,1664,5446,78,1,10.0,1,Rodrigo Mejia,Unknown,True,feature
ONE-1329,- Update transactions name in movements section (pull request 735),source/Modules,2024-12-02,2024-12-02,1,143,10,153,5,1,4.33,1,Julio Rico,Unknown,True,maintenance
ONE-1335,- Error while requesting an additional credit card (pull request 727),source/Modules,2024-11-25,2024-11-25,1,2,6,8,2,1,1.38,1,Julio Rico,Unknown,True,feature
ONE-1336,- Use new face auth endpoint for password recovery from login screen (pull request 725),source/Modules,2024-11-25,2024-11-25,1,162,30,192,17,1,10.0,1,Rodrigo Mejia,Unknown,True,feature
ONE-1345,- Add missing identity validation checkpoint to user recovery flow (pull request 730),source/Components,2024-11-26,2024-11-26,1,164,30,194,8,2,6.54,1,Rodrigo Mejia,Unknown,True,feature
ONE-1384,- Include the missing remote config parameter to decode the response. (pull request 732),source/Core,2024-11-27,2024-11-27,1,2,1,3,1,1,0.83,1,Emely Melgar,Unknown,True,other
ONE-1388,- Move the isLoading position to hidden when an error or success occurs. (pull request 737),source/Components,2024-11-29,2024-11-29,1,2,1,3,1,1,0.83,1,Emely Melgar,Unknown,True,other
ONE-1390,- Onboarding Survey is not sorting correctly answers,tests,2024-12-31,2024-12-31,1,220,4,224,10,1,7.54,1,Julio Rico,Unknown,True,testing
ONE-1391,- Send Notification for credential alert presentation,source/Modules,2024-12-31,2024-12-31,1,34,3,37,6,1,3.67,1,Vladimir Guevara,Unknown,True,other
ONE-1392,- Disable continue before option selection on survey flow (pull request 776),source/Modules,2025-01-06,2025-01-06,1,7,0,7,2,1,1.37,1,Rodrigo Mejia,Unknown,True,other
ONE-1393,- Display alert for changing password after using temporary credentials,source/Components,2025-01-02,2025-01-02,1,216,17,233,9,1,7.13,1,Julio Rico,Unknown,True,other
ONE-1397,- Add new repository specs for cocoapods dependencies,other,2024-12-04,2024-12-04,1,10,10,20,1,1,1.0,1,Vladimir Guevara,Unknown,True,feature
ONE-1398,- Add new parameter to apply mask on OneTextFieldConfiguration,source/Modules,2025-01-23,2025-01-23,1,3,0,3,2,1,1.33,1,Vladimir Guevara,Unknown,True,feature
ONE-1400,- Sort Atlantida folder root files by type. (pull request 743),ios_code,2024-12-05,2024-12-05,1,39,31,70,1238,1,10.0,1,Emely Melgar,Unknown,True,other
ONE-1401,- Removing dead code (pull request 789),legacy,2025-01-16,2025-01-16,1,864,24082,24946,447,1,10.0,1,Edgar Emilio Vásquez Castillo,Unknown,True,testing
ONE-1405,- Add new method in CivilStatusInteractor to navigate directly to user housing,source/Modules,2024-12-26,2024-12-26,1,32,13,45,8,1,4.75,1,Vladimir Guevara,Unknown,True,feature
ONE-1410,- Include a new validation to clear the DUIAuth header only if the request is successful. (pull request 740),source/Core,2024-12-03,2024-12-03,1,28,16,44,2,1,1.74,1,Emely Melgar,Unknown,True,feature
ONE-1411,- Add OnHold Event when the offer is Pending (pull request 752),source/Core,2024-12-19,2024-12-19,1,19,1,20,4,1,2.5,1,Edgar Emilio Vásquez Castillo,Unknown,True,feature
ONE-1413,- Update payment options for blocked credit cards (pull request 781),source/Modules,2024-12-19,2025-01-10,23,1661,435,2096,26,3,10.0,1,Julio Rico,Unknown,True,testing
ONE-1415,- Fix no action on back tap on additional card document scan (pull request 757),source/Modules,2024-12-24,2024-12-24,1,3,1,4,1,1,0.84,1,Rodrigo Mejia,Unknown,True,feature
ONE-1417,- Fix Lifemiles points transfer flow (pull request 1010),source/Modules,2025-06-10,2025-06-25,16,333,120,453,13,2,10.0,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-1422,- PART 1 Remove unused files. (pull request 786),legacy,2025-01-14,2025-01-14,1,411,4556,4967,398,1,10.0,1,Emely Melgar,Unknown,True,other
ONE-1432,- Disable animated navigation for cardProcess,legacy,2025-01-21,2025-01-21,1,1,1,2,1,1,0.82,1,Vladimir Guevara,Unknown,True,other
ONE-1433,- add hide data label to carousel card. (pull request 1111),source/Modules,2025-08-27,2025-08-27,1,34,2,36,3,1,2.16,1,Javier Lorenzana,Unknown,True,feature
ONE-1435,- Add new frame for card credit limit presentation in CreditCardOfferDetailsView,source/Modules,2024-12-12,2024-12-12,1,1,1,2,1,1,0.82,1,Vladimir Guevara,Unknown,True,feature
ONE-1448,- Add missing email property on ccast1 call (pull request 767),source/OTP,2024-12-30,2024-12-30,1,8,6,14,1,2,1.24,1,Rodrigo Mejia,Unknown,True,feature
ONE-1454,- Modifies third delivery attempt failed alert button title,legacy,2024-12-19,2024-12-26,8,18,6,24,3,2,2.34,1,Vladimir Guevara,Unknown,True,other
ONE-1456,_- Fix protection plan UI and request issues. (pull request 754),source/Modules,2024-12-20,2024-12-20,1,199,161,360,14,1,10.0,1,Emely Melgar,Unknown,True,bugfix
ONE-1457,- Solve bug with apperance of the navigation bar (pull request 756),source/Components,2024-12-17,2024-12-20,4,133,31,164,10,2,7.24,1,Edgar Emilio Vásquez Castillo,Unknown,True,bugfix
ONE-1460,- Fix message for blocked card,source/Modules,2024-12-18,2024-12-18,1,72,6,78,2,1,2.08,1,Julio Rico,Unknown,True,bugfix
ONE-1461,"- Display credit card transactions, including those from blocked credit cards. (pull request 823)",source/Core,2025-02-12,2025-02-12,1,233,52,285,10,1,8.15,1,Julio Rico,Unknown,True,other
ONE-1466,- Remove unused image assets from Xcode project (pull request 793),other,2025-01-17,2025-01-17,1,21,3068,3089,345,1,10.0,1,Rodrigo Mejia,Unknown,True,other
ONE-1469,Merged in bugfix(pull request 765),source/Components,2025-01-02,2025-01-02,1,18,5,23,2,1,1.53,1,Edgar Emilio Vásquez Castillo,Unknown,True,bugfix
ONE-1470,_ONE 1471 - Fix the card payment method defects. (pull request 773),source/Modules,2024-12-23,2025-01-02,11,72,42,114,6,2,4.74,2,"Edgar Emilio Vásquez Castillo, Emely Melgar",Unknown,True,bugfix
ONE-1472,- Include missing protection plan text. (pull request 762),source/Modules,2024-12-23,2024-12-23,1,12,1,13,2,1,1.43,1,Emely Melgar,Unknown,True,other
ONE-1482,- Fix text for protection plan (pull request 758),source/Modules,2024-12-24,2024-12-24,1,1,1,2,1,1,0.82,1,Julio Rico,Unknown,True,bugfix
ONE-1484,- Enable credit card menu options for blocked credit cards (pull request 782),source/Modules,2025-01-10,2025-01-10,1,1,89,90,7,1,4.7,1,Julio Rico,Unknown,True,testing
ONE-1485,- Fix hiding banners in credit card menu (pull request 788),source/Modules,2025-01-29,2025-01-29,1,141,42,183,11,1,7.63,1,Julio Rico,Unknown,True,testing
ONE-1490,- Fix wrong parameters in password reset OTP validation call (pull request 775),source/OTP,2025-01-06,2025-01-06,1,3,3,6,1,1,0.86,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-1492,ONE 1493 - Add resend toast and max attempts handling on OTP flows (pull request 772),source/OTP,2025-01-03,2025-01-03,1,18,4,22,5,1,3.02,1,Rodrigo Mejia,Unknown,True,feature
ONE-1498,- ONEAPP-- Receive memberId as String (pull request 768),source/Modules,2025-01-03,2025-01-03,1,4,5,9,4,1,2.39,1,José De la O,Unknown,True,other
ONE-1499,- Credit Card Points Transfer - Navigation Flow Update (pull request 797),source/Modules,2025-01-22,2025-01-22,1,75,40,115,7,1,4.95,1,Julio Rico,Unknown,True,maintenance
ONE-1500,- Add missing max beneficiaries disclaimer message (pull request 785),source/Modules,2025-01-14,2025-01-14,1,62,21,83,4,1,3.13,1,Rodrigo Mejia,Unknown,True,testing
ONE-1505,- Fix wrong error handling on otp resend action (pull request 777),source/OTP,2025-01-06,2025-01-06,1,46,8,54,5,1,3.34,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-1508,- Add locale to the converters and extensions (pull request 779),legacy,2025-01-08,2025-01-08,1,34,46,80,9,1,5.6,1,Edgar Emilio Vásquez Castillo,Unknown,True,feature
ONE-1509,- Change voice tone for user unrecognized alert,source/Modules,2025-01-08,2025-01-08,1,85,12,97,2,1,2.27,1,Vladimir Guevara,Unknown,True,other
ONE-1511,ONE1521 - Implement Firebase Distribution(pull request 796),ci_cd,2025-01-21,2025-01-21,1,44,0,44,3,1,2.24,1,Edgar Emilio Vásquez Castillo,Unknown,True,feature
ONE-1513,- Fix alert message for main cancellation card. (4),tests,2025-09-25,2025-09-25,1,350,73,423,13,1,10.0,1,jrico-applaudo,Unknown,True,testing
ONE-1516,- Remove onAppear extension and include NonSecureViewIdentifier. (pull request 840),source/Modules,2025-02-18,2025-02-21,4,398,111,509,23,3,10.0,1,Emely Melgar,Unknown,True,other
ONE-1521,- Isolate the firebase distribution step (pull request 798),other,2025-01-21,2025-01-21,1,24,2,26,1,1,1.06,1,Edgar Emilio Vásquez Castillo,Unknown,True,other
ONE-1526,- Fix message text for Complain section (pull request 783),source/Modules,2025-01-10,2025-01-10,1,1,1,2,1,1,0.82,1,Julio Rico,Unknown,True,bugfix
ONE-1529,- Handling error scenario cancel a credit card (pull request 787),source/Modules,2025-01-15,2025-01-15,1,3,1,4,1,1,0.84,1,Julio Rico,Unknown,True,other
ONE-1531,- Add live search support to the picker list with query search (pull request 810),source/DesignSystem,2025-01-31,2025-01-31,1,513,109,622,32,1,10.0,1,Edgar Emilio Vásquez Castillo,Unknown,True,testing
ONE-1545,- Add new additional card information editing flow (pull request 809),source/Modules,2025-02-01,2025-02-01,1,1214,105,1319,25,1,10.0,1,Rodrigo Mejia,Unknown,True,testing
ONE-1547,- Redesign Contanos mas de vos screen (pull request 815),source/Modules,2025-02-03,2025-02-03,1,725,6,731,12,1,10.0,1,Vladimir Guevara,Unknown,True,other
ONE-1552,- Recovery custom navigation delete items. (pull request 790),legacy,2025-01-16,2025-01-16,1,30,0,30,1,1,1.1,1,Emely Melgar,Unknown,True,other
ONE-1555,- Update Onboarding screens to match Figma design (pull request 826),source/Modules,2025-03-19,2025-03-19,1,165,46,211,6,1,5.41,1,Julio Rico,Unknown,True,maintenance
ONE-1556,- Show the credit card holders name in the selector within the Credit Card Options section (pull request 824),source/Modules,2025-02-12,2025-02-12,1,28,21,49,2,1,1.79,1,Julio Rico,Unknown,True,other
ONE-1557,- Remove unused files and move the deactivated modules into new path. (pull request 803),legacy,2025-01-28,2025-01-28,1,616,13392,14008,445,1,10.0,1,Emely Melgar,Unknown,True,testing
ONE-1558,- Update the error message to indicate that the document has expired. (pull request 805),source/Modules,2025-01-27,2025-01-27,1,13,2,15,2,1,1.45,1,Julio Rico,Unknown,True,maintenance
ONE-1563,- Optimization in complaint handling flow (pull request 910),source/Modules,2025-05-23,2025-05-23,1,2022,741,2763,42,1,10.0,1,Javier Lorenzana,Unknown,True,testing
ONE-1601,- Fix issue while transfer points on credit card on last step (pull request 799),source/Modules,2025-01-22,2025-01-22,1,5,4,9,1,1,0.89,1,Julio Rico,Unknown,True,bugfix
ONE-1641,- Adjust recoveryCredentialButton tappable area in LoginPage,source/DesignSystem,2025-01-27,2025-01-30,4,63,78,141,7,2,5.51,1,Vladimir Guevara,Unknown,True,other
ONE-1652,- Revert usage of logApplicationNameApp for description in log section. (10),source/Modules,2025-09-26,2025-09-29,4,2,2,4,1,2,1.14,1,jrico-applaudo,Unknown,True,other
ONE-1655,- Fix calendar behavior in Mis Movimientos view (pull request 883),source/Components,2025-03-21,2025-03-21,1,67,56,123,3,1,3.03,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-1657,- Fix last item selected on OneWheelPicker (pull request 808),source/Components,2025-01-30,2025-01-30,1,15,5,20,1,1,1.0,1,Julio Rico,Unknown,True,feature
ONE-1658,- Part2 Refactor the Dynatrace implementation to use parent actions instead of child actions. (pull request 973),source/Modules,2025-05-29,2025-06-04,7,1333,1192,2525,143,2,10.0,1,Emely Melgar,Unknown,True,testing
ONE-1661,- Change shortImage to contextImage. (pull request 807),source/Modules,2025-01-29,2025-01-29,1,6,6,12,3,1,1.92,1,Emely Melgar,Unknown,True,other
ONE-1664,- Fix padding for error message in OTPCredentialRecoveryInputView (pull request 814),source/OTP,2025-02-05,2025-02-05,1,5,4,9,1,1,0.89,1,Julio Rico,Unknown,True,feature
ONE-1665,- Update flag for back navigation (pull request 820),source/Components,2025-01-30,2025-02-07,9,8,3,11,2,2,1.71,1,Julio Rico,Unknown,True,maintenance
ONE-1666,- Update the UI for the customer references flow in onboarding (pull request 961),source/Modules,2025-05-26,2025-05-26,1,1474,874,2348,32,1,10.0,1,Josseh Blanco,Unknown,True,maintenance
ONE-1667,- Delete unused files from views folder and move unoptimized files to new file structure (pull request 816),legacy,2025-02-04,2025-02-04,1,287,6674,6961,166,1,10.0,1,Rodrigo Mejia,Unknown,True,feature
ONE-1679,- Fix unit test lane with Xcode 16 (pull request 835),resources,2025-02-19,2025-02-20,2,320,29698,30018,209,4,10.0,1,Edgar Emilio Vásquez Castillo,Unknown,True,testing
ONE-1683,- Adjust Alignment of LoginPage header,source/Modules,2025-01-31,2025-01-31,1,2,2,4,1,1,0.84,1,Vladimir Guevara,Unknown,True,other
ONE-1693,- Optimization in Menu of the application (pull request 914),source/Modules,2025-05-27,2025-05-27,1,700,154,854,17,1,10.0,1,José Miguel Rivera López,Unknown,True,testing
ONE-1694,- Optimization in Configurations Menu (pull request 975),source/Modules,2025-05-29,2025-06-09,12,782,282,1064,18,2,10.0,2,"Javier Lorenzana, Miguel Rivera",Unknown,True,testing
ONE-1703,- Optimization in Trusted Devices List (pull request 991),source/Modules,2025-06-19,2025-06-19,1,495,203,698,13,1,10.0,1,Javier Lorenzana,Unknown,True,testing
ONE-1707,- Add missing force item value on the companies picker list (pull request 819),source/Modules,2025-02-06,2025-02-06,1,13,11,24,1,1,1.04,1,Edgar Emilio Vásquez Castillo,Unknown,True,feature
ONE-1708,- Fix navigation logic in additional card information flow (pull request 821),source/Modules,2025-02-07,2025-02-07,1,1,1,2,1,1,0.82,1,Rodrigo Mejia,Unknown,True,feature
ONE-1712,- Re-add missing offers asset (pull request 822),source/DesignSystem,2025-02-11,2025-02-11,1,48,0,48,7,1,4.28,1,Rodrigo Mejia,Unknown,True,feature
ONE-1716,- Part 2 Add the new UI for the Accounts Beneficiaries View (pull request 943),source/Components,2025-05-16,2025-05-19,4,1618,561,2179,43,2,10.0,1,Josseh Blanco,Unknown,True,testing
ONE-1719,- Add the edit and delete beneficiary functionalities (pull request 953),source/Modules,2025-05-22,2025-05-22,1,242,25,267,13,1,9.47,1,Josseh Blanco,Unknown,True,feature
ONE-1720,- Mejoras ScanDUI UIUX (pull request 917),source/Modules,2025-04-28,2025-04-28,1,176,36,212,12,1,8.42,1,Edgar Emilio Vásquez Castillo,Unknown,True,other
ONE-1735,- Display previously saved phone number in working place. (pull request 926),source/Components,2025-05-09,2025-05-09,1,126,5,131,7,1,5.11,1,Julio Rico,Unknown,True,testing
ONE-1739,- Refactor the protection plan contract to include the new services and the missing fraudProtectionID. (pull request 828),source/Modules,2025-02-14,2025-02-14,1,61,79,140,4,1,3.7,1,Emely Melgar,Unknown,True,feature
ONE-1749,- Fix issue with Tracking Card section (pull request 830),source/Core,2025-02-18,2025-02-18,1,4,2,6,2,1,1.36,1,Julio Rico,Unknown,True,bugfix
ONE-1751,- Improve the text rendering for the Terms and FATCA sections. (pull request 829),source/Modules,2025-02-18,2025-02-18,1,2,2,4,2,1,1.34,1,Julio Rico,Unknown,True,other
ONE-1761,- Fix text copies in Unrecognized Transactions section for Credit card. (pull request 1137),source/Modules,2025-09-11,2025-09-11,1,5,2,7,3,1,1.87,1,Julio Rico,Unknown,True,bugfix
ONE-1763,- Save Acceptance Status when users rejects terms and,source/Modules,2025-02-18,2025-02-18,1,1,0,1,1,1,0.81,1,Vladimir Guevara,Unknown,True,other
ONE-1765,- Include architecture files for BillableAccount (pull request 833),source/Modules,2025-02-19,2025-02-19,1,403,5,408,13,1,10.0,1,Emely Melgar,Unknown,True,other
ONE-1766,- Remove unwanted animation and improve navigation logic (pull request 852),source/Components,2025-02-24,2025-03-04,9,2619,83,2702,53,3,10.0,1,Rodrigo Mejia,Unknown,True,testing
ONE-1768,- Fix animation and spacing for empty state (pull request 870),other,2025-02-27,2025-03-17,19,4447,102,4549,56,6,10.0,1,Julio Rico,Unknown,True,testing
ONE-1771,- Update colector form to use form domains. (pull request 872),source/Modules,2025-02-22,2025-03-17,24,1331,413,1744,28,6,10.0,2,"Emely Melgar, Vladimir Guevara",Unknown,True,testing
ONE-1772,- Sync - AddBackgroundEmbeddedNavigation (pull request 933),source/Components,2025-03-04,2025-05-08,66,2304,289,2593,54,9,10.0,1,Edgar Emilio Vásquez Castillo,Unknown,True,testing
ONE-1774,- Include payment interactor and billable ticket. (pull request 888),source/Modules,2025-02-25,2025-03-27,31,1188,105,1293,40,2,10.0,1,Emely Melgar,Unknown,True,testing
ONE-1776,- Fix Bill Dynamic form UI (pull request 880),source/Modules,2025-03-06,2025-03-18,13,556,347,903,23,4,10.0,1,Rodrigo Mejia,Unknown,True,testing
ONE-1796,- Additional card order fix for name and credit limit (pull request 955),source/Modules,2025-05-23,2025-05-23,1,66,26,92,5,1,3.72,1,Julio Rico,Unknown,True,feature
ONE-1798,- Fix navigation issue with editing address in personal data section (pull request 963),source/Modules,2025-05-27,2025-05-27,1,1,1,2,1,1,0.82,1,Julio Rico,Unknown,True,feature
ONE-1799,- Fix issue when updating phone number for personal references (pull request 934),source/Modules,2025-05-12,2025-05-12,1,50,30,80,3,1,2.6,1,Julio Rico,Unknown,True,bugfix
ONE-1800,- Ensure credit card carousel retains visible card on navigation (pull request 962),source/Modules,2025-05-27,2025-05-27,1,15,10,25,1,1,1.05,1,Julio Rico,Unknown,True,other
ONE-1801,- Remove the disabled condition for Transactions option. (pull request 841),source/Modules,2025-02-21,2025-02-21,1,0,2,2,1,1,0.82,1,Emely Melgar,Unknown,True,other
ONE-1803,- Third Attempt Message Not Displayed After Sign-In (pull request 937),source/Modules,2025-05-14,2025-05-14,1,1,0,1,1,1,0.81,1,Julio Rico,Unknown,True,other
ONE-1820,- Automatic payments and Empty state section (pull request 865),source/Modules,2025-04-07,2025-04-07,1,945,14,959,25,1,10.0,1,Julio Rico,Unknown,True,testing
ONE-1823,- Add automatic payment account affiliation form (pull request 881),source/Modules,2025-04-08,2025-04-08,1,1083,18,1101,29,1,10.0,1,Rodrigo Mejia,Unknown,True,testing
ONE-1825,- Remove vertical white spaces in AutoPayEnrollmentView(pull request 936),source/Modules,2025-04-08,2025-05-14,37,1452,80,1532,30,3,10.0,1,Julio Rico,Unknown,True,testing
ONE-1827,- Part 3 - Include automatic services payment feature. (pull request 938),source/Modules,2025-05-02,2025-05-20,19,2685,1102,3787,164,3,10.0,1,Emely Melgar,Unknown,True,testing
ONE-1828,- Include cancel automatic services contract. (pull request 941),source/Modules,2025-05-23,2025-05-23,1,547,65,612,20,1,10.0,1,Emely Melgar,Unknown,True,testing
ONE-1829,- Include edit automatic services contract. (pull request 949),source/Modules,2025-05-27,2025-05-27,1,1161,937,2098,47,1,10.0,1,Emely Melgar,Unknown,True,testing
ONE-1830,- Payment history (pull request 907),source/Modules,2025-04-24,2025-04-24,1,1640,18,1658,46,1,10.0,1,Julio Rico,Unknown,True,testing
ONE-1834,- Update data in selector in Credit Card Transactions (pull request 850),source/Modules,2025-02-28,2025-02-28,1,3,3,6,1,1,0.86,1,Julio Rico,Unknown,True,maintenance
ONE-1844,- Move the PEP condition coordinator before the AFP flow (pull request 897),source/Modules,2025-04-03,2025-04-03,1,484,343,827,32,1,10.0,1,Josseh Blanco,Unknown,True,other
ONE-1857,- Add more detailed error descriptions for the dynatrace requestresponse logs (pull request 878),source/Core,2025-03-24,2025-03-24,1,623,47,670,17,1,10.0,1,Josseh Blanco,Unknown,True,testing
ONE-1862,- Add documentation for ExpiredSessionSingleton and UserSessionManager (pull request 889),legacy,2025-03-28,2025-03-28,1,117,3,120,2,1,2.5,1,Josseh Blanco,Unknown,True,feature
ONE-1866,- Add new Map floater logic and UI (pull request 902),source/Modules,2025-04-08,2025-04-08,1,67,17,84,5,1,3.64,1,Rodrigo Mejia,Unknown,True,feature
ONE-1867,"- Update alerts for FaceAuthentication, ForNickname and Extension (pull request 894)",source/Modules,2025-04-03,2025-04-03,1,682,99,781,21,1,10.0,1,Rodrigo Mejia,Unknown,True,maintenance
ONE-1871,- Upgrade FraudForce (pull request 884),other,2025-03-24,2025-03-24,1,5,5,10,2,1,1.4,1,Edgar Emilio Vásquez Castillo,Unknown,True,maintenance
ONE-1872,- Add SelectOne on Account Form (pull request 882),source/{ => Modules,2025-03-21,2025-03-21,1,247,149,396,54,1,10.0,1,Edgar Emilio Vásquez Castillo,Unknown,True,feature
ONE-1873,- Remove type assignment recursion from session log list. (pull request 879),source/Components,2025-03-18,2025-03-18,1,0,1,1,1,1,0.81,1,Emely Melgar,Unknown,True,other
ONE-1876,- Add new face validation alerts (pull request 909),source/Modules,2025-04-16,2025-04-16,1,1330,1099,2429,20,1,10.0,1,Rodrigo Mejia,Unknown,True,feature
ONE-1904,- New biometry error messages (pull request 893),source/Modules,2025-04-03,2025-04-03,1,280,8,288,5,1,5.68,1,Julio Rico,Unknown,True,feature
ONE-1907,- Display error message as toast (pull request 912),source/OTP,2025-04-21,2025-04-21,1,8,5,13,1,1,0.93,1,Julio Rico,Unknown,True,other
ONE-1923,- Change the check point template for the caapplicationst1 call (pull request 885),source/Modules,2025-03-24,2025-03-24,1,1,1,2,1,1,0.82,1,Josseh Blanco,Unknown,True,other
ONE-1943,- Fix users being stuck on the contract list in onboarding after an error,source/Modules,2025-03-28,2025-03-28,1,10,5,15,3,1,1.95,1,Josseh Blanco,Unknown,True,bugfix
ONE-1944,- Fix navigation routing for selfie and document scan. (pull request 900),source/Modules,2025-04-04,2025-04-04,1,24,3,27,2,1,1.57,1,Emely Melgar,Unknown,True,bugfix
ONE-1950,- Add Error Alert on Checkpoint Payment (pull request 892),source/Modules,2025-04-02,2025-04-02,1,117,7,124,6,1,4.54,1,Edgar Emilio Vásquez Castillo,Unknown,True,feature
ONE-1961,- Update the date format for the bill detail. (pull request 895),source/Modules,2025-04-02,2025-04-02,1,3,2,5,1,1,0.85,1,Emely Melgar,Unknown,True,maintenance
ONE-1963,- Add scrollview centered content (pull request 899),source/Modules,2025-04-03,2025-04-03,1,98,57,155,9,1,6.35,1,Edgar Emilio Vásquez Castillo,Unknown,True,feature
ONE-1964,- Add the refresh session error alert (pull request 916),source/Core,2025-04-24,2025-04-24,1,556,62,618,28,1,10.0,1,Josseh Blanco,Unknown,True,testing
ONE-1965,- Remove ExpiredSessionSingleton and manage the authenticated session with the UserSessionManager (pull request 908),source/Modules,2025-04-15,2025-04-15,1,830,848,1678,40,1,10.0,1,Josseh Blanco,Unknown,True,testing
ONE-1973,- Remove Facephi logo form selfie and document widget. (pull request 898),resources,2025-04-03,2025-04-03,1,0,0,0,2,1,1.3,1,Emely Melgar,Unknown,True,other
ONE-2000,"- Components improvement (BillPaymentInfo, Button-Hidding and SwipeBack) (pull request 904)",source/Modules,2025-04-14,2025-04-14,1,493,473,966,24,1,10.0,1,Edgar Emilio Vásquez Castillo,Unknown,True,testing
ONE-2004,- Include the new zip without the default tutorial for FacePhi. (pull request 901),resources,2025-04-04,2025-04-04,1,0,0,0,2,1,1.3,1,Emely Melgar,Unknown,True,feature
ONE-2031,- Fix Biometry alerts body bullets alignment (pull request 1119),source/Components,2025-08-28,2025-08-28,1,140,57,197,10,1,7.27,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-2032,- Include help alert for workplace onboarding flow. (pull request 915),source/{ => Modules,2025-04-23,2025-04-23,1,331,61,392,46,1,10.0,1,Emely Melgar,Unknown,True,other
ONE-2042,- Optimization in the User Activity Logs flow (pull request 1009),source/Modules,2025-07-08,2025-07-08,1,760,329,1089,17,1,10.0,1,Javier Lorenzana,Unknown,True,testing
ONE-2043,- Terms and Conditions Optimization (pull request 1019),source/Modules,2025-07-17,2025-07-17,1,541,295,836,17,1,10.0,1,José Miguel Rivera López,Unknown,True,testing
ONE-2044,- Card Tracking Optimization (pull request 1046),source/Modules,2025-07-23,2025-07-23,1,581,214,795,20,1,10.0,1,Javier Lorenzana,Unknown,True,testing
ONE-2046,- Refactor and reorganize component HomeView and HomeCardDetailView to apply shimmering effect,source/Modules,2025-04-28,2025-04-28,1,202,47,249,6,2,6.09,1,Julio Rico,Unknown,True,refactor
ONE-2052,- Payment service - UI Improvements (pull request 928),source/Modules,2025-05-08,2025-05-08,1,81,38,119,16,1,9.49,1,Edgar Emilio Vásquez Castillo,Unknown,True,other
ONE-2068,- Add new Pay In Installment flow (pull request 942),source/Modules,2025-05-22,2025-05-22,1,1839,16,1855,38,1,10.0,1,Rodrigo Mejia,Unknown,True,testing
ONE-2076,- Fix voicing in the refresh session error alert (pull request 918),source/Components,2025-04-25,2025-04-25,1,2,2,4,1,1,0.84,1,Josseh Blanco,Unknown,True,bugfix
ONE-2078,- Fix users being sent to the guest view despite having logged in before (pull request 921),source/Core,2025-04-28,2025-04-28,1,25,15,40,8,1,4.7,1,Josseh Blanco,Unknown,True,testing
ONE-2079,- Add a loading message to the refresh session alert for authenticated users (pull request 923),source/Core,2025-04-29,2025-04-29,1,40,7,47,7,1,4.27,1,Josseh Blanco,Unknown,True,feature
ONE-2082,- Update localized string for job info. (pull request 924),source/Modules,2025-04-29,2025-04-29,1,12,1,13,2,1,1.43,1,Emely Melgar,Unknown,True,maintenance
ONE-2092,- Include Services Payment Automatic Payment Features. (pull request 967),source/Modules,2025-05-27,2025-05-28,2,19739,261,20000,297,2,10.0,2,"Emely Melgar, Josseh Blanco",Unknown,True,testing
ONE-2212,- Remove Appcenter Stage (pull request 929),other,2025-05-07,2025-05-07,1,0,31,31,1,1,1.11,1,Edgar Emilio Vásquez Castillo,Unknown,True,other
ONE-2222,- Fix view overflow on account statement flow (pull request 992),source/Modules,2025-06-10,2025-06-12,3,263,95,358,5,4,7.28,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-2223,- Add support for displaying Blocked and Repositioned Credit Cards in the Home Carousel (pull request 945),source/Modules,2025-05-21,2025-05-21,1,568,221,789,16,1,10.0,1,Julio Rico,Unknown,True,feature
ONE-2224,- Add new max characters error message in address selection view (pull request 956),source/Modules,2025-05-23,2025-05-23,1,44,1,45,4,1,2.75,1,Rodrigo Mejia,Unknown,True,feature
ONE-2259,- Include CustomPageIndicatorStyleConfiguration.,source/Modules,2025-06-05,2025-09-30,118,24799,8863,33662,1045,41,10.0,1,Emely Melgar,Unknown,True,testing
ONE-2261,- Fix scrolling behavior on card payment screen (pull request 964),source/Modules,2025-05-28,2025-05-28,1,81,15,96,4,1,3.26,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-2262,- Fix syntax error. (pull request 1032),source/Modules,2025-07-03,2025-07-04,2,63,54,117,4,2,3.77,2,"Emely Melgar, Julio Rico",Unknown,True,bugfix
ONE-2263,- Fix Scrolling Issues in Personal Information When Using Large Font Sizes (pull request 1016),source/Modules,2025-07-08,2025-07-08,1,18,18,36,1,1,1.16,1,Javier Lorenzana,Unknown,True,bugfix
ONE-2264,- Scrolling Problems - General Information - with Large Font Size (pull request 1036),source/Modules,2025-07-16,2025-07-16,1,133,18,151,5,1,4.31,1,José Miguel Rivera López,Unknown,True,other
ONE-2265,- Part 2 Add accessibility for larger fonts in Contacts section. (pull request 1013),source/Modules,2025-07-03,2025-07-03,1,221,43,264,7,2,6.74,1,Julio Rico,Unknown,True,feature
ONE-2266,- Part 2 Add accessibility for larger fonts in Reset password section. (pull request 1020),source/Modules,2025-07-02,2025-07-03,2,38,37,75,3,2,2.85,1,Julio Rico,Unknown,True,feature
ONE-2268,- Fix scrolling in user recovery screen (pull request 1044),source/Modules,2025-07-17,2025-07-17,1,4,4,8,1,1,0.88,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-2279,- Set CUID valor explicity with customerID (pull request 940),source/Core,2025-05-19,2025-05-19,1,24,2,26,8,1,4.56,1,Edgar Emilio Vásquez Castillo,Unknown,True,other
ONE-2286,- Improve on deleting on OTP verification code. (pull request 1118),source/Modules,2025-08-29,2025-08-29,1,181,11,192,8,1,6.22,1,Julio Rico,Unknown,True,other
ONE-2287,- Fix alerts that use the statusResponseError or statusRequestError (pull request 947),source/Core,2025-05-20,2025-05-20,1,29,12,41,3,1,2.21,1,Josseh Blanco,Unknown,True,bugfix
ONE-2292,- Migrate to new Amplitude Version (SPM) (pull request 978),source/Core,2025-06-03,2025-06-03,1,119,84,203,9,1,6.83,1,Rodrigo Mejia,Unknown,True,testing
ONE-2305,- Change FaceAuthentication and Fix PaymentPicker (pull request 970),source/Modules,2025-05-27,2025-06-02,7,131,80,211,6,2,5.71,2,"Edgar Emilio Vásquez Castillo, Emilio Vasquez",Unknown,True,testing
ONE-2306,- Disable Input and Selectable explicitly on AutomaticPayment (pull request 974),source/Modules,2025-06-02,2025-06-02,1,52,15,67,5,1,3.47,1,Edgar Emilio Vásquez Castillo,Unknown,True,other
ONE-2312,- Add missing items to match Android Add(pull request 1005),source/Modules,2025-06-20,2025-06-20,1,26,1,27,4,1,2.57,1,Edgar Emilio Vásquez Castillo,Unknown,True,feature
ONE-2314,- Localize strings and match to the figma (pull request 950),source/Modules,2025-05-21,2025-05-21,1,36,3,39,2,1,1.69,1,Edgar Emilio Vásquez Castillo,Unknown,True,other
ONE-2315,"- Update text copies in Surveys, Contracts and Delivery options (pull request 1129)",source/Modules,2025-09-04,2025-09-04,1,298,40,338,9,1,8.18,1,Julio Rico,Unknown,True,maintenance
ONE-2322,- Add changes due the E2E Automatic Payment (pull request 996),source/Modules,2025-06-16,2025-06-16,1,193,118,311,33,1,10.0,1,Edgar Emilio Vásquez Castillo,Unknown,True,testing
ONE-2324,- Fixes on copy text in first login section. (pull request 1149),source/Modules,2025-09-18,2025-09-19,2,331,98,429,11,5,10.0,1,Julio Rico,Unknown,True,testing
ONE-2336,- Set default selection card on Card Options View when the main card is not active (pull request 989),source/Core,2025-06-12,2025-06-12,1,253,5,258,6,1,5.88,1,Julio Rico,Unknown,True,testing
ONE-2348,- Add native tab bar and optimize the home view (pull request 990),source/Modules,2025-05-28,2025-06-12,16,7536,4797,12333,393,34,10.0,1,Josseh Blanco,Unknown,True,testing
ONE-2358,- Remove the associated CoordinatorTab value,source/Modules,2025-05-30,2025-05-30,1,60,25,85,6,1,4.15,1,Josseh Blanco,Unknown,True,other
ONE-2363,- Add Skeleton effect loading state to Menu section. (pull request 986),source/Components,2025-06-13,2025-06-13,1,56,17,73,10,1,6.03,1,Julio Rico,Unknown,True,feature
ONE-2364,- Add Skeleton effect loading state to home cards. (pull request 977),source/Components,2025-06-05,2025-06-05,1,202,23,225,11,1,8.05,1,Julio Rico,Unknown,True,feature
ONE-2369,- Modify card cancellation dialog text (pull request 1096),source/Modules,2025-08-12,2025-08-12,1,1,1,2,1,1,0.82,1,Javier Lorenzana,Unknown,True,other
ONE-2371,- Add customer support info support with remote notification. (pull request 1034),other,2025-07-08,2025-07-08,1,776,447,1223,85,1,10.0,1,Edgar Emilio Vásquez Castillo,Unknown,True,testing
ONE-2374,- Use light theme for native bottom sheets (pull request 968),source/Components,2025-05-30,2025-05-30,1,84,9,93,5,1,3.73,1,Josseh Blanco,Unknown,True,other
ONE-2376,- Refactor Login Response Data,source/Modules,2025-05-29,2025-06-04,7,19329,355,19684,317,3,10.0,1,Miguel Rivera,Unknown,True,testing
ONE-2377,- Unify the Profile and GetLastLogin Endpoint,source/Core,2025-06-04,2025-06-04,1,48,41,89,6,1,4.19,1,Miguel Rivera,Unknown,True,other
ONE-2378,- Refactor Login Response Data,source/Core,2025-05-29,2025-06-04,7,135,137,272,7,3,7.12,1,Miguel Rivera,Unknown,True,refactor
ONE-2386,- Update text copy for recover userupdate password (pull request 1039),source/Components,2025-07-08,2025-07-08,1,1,1,2,1,1,0.82,1,Julio Rico,Unknown,True,maintenance
ONE-2387,- Refactor the BillableAutomatic update form to support the notification channel and request. (pull request 1003),source/Modules,2025-06-19,2025-06-19,1,199,93,292,11,1,8.72,1,Emely Melgar,Unknown,True,testing
ONE-2394,- Add custom encoderdecoder for OneFieldType and OneFieldDataType. (pull request 1108),source/Core,2025-08-20,2025-08-20,1,12,0,12,1,1,0.92,1,Julio Rico,Unknown,True,feature
ONE-2424,- Managing unified endpoints LogIn (pull request 1006),source/Core,2025-06-24,2025-06-24,1,122,261,383,15,1,10.0,1,José Miguel Rivera López,Unknown,True,testing
ONE-2451,- Add the shimmering effect to the Home View components (pull request 1002),source/Modules,2025-06-19,2025-06-19,1,208,139,347,11,1,9.27,1,Josseh Blanco,Unknown,True,feature
ONE-2452,- Add the retry states in the Home screen (pull request 1008),source/Modules,2025-06-25,2025-06-25,1,303,98,401,13,1,10.0,1,Josseh Blanco,Unknown,True,feature
ONE-2455,- Retry state for skeleton effect in Main Menu and Credit Card Menu sections (pull request 1004),source/Modules,2025-06-20,2025-06-20,1,161,78,239,15,1,10.0,1,Julio Rico,Unknown,True,testing
ONE-2457,- AFP - Hide back button on SelfieScanView (pull request 1080),source/Modules,2025-07-30,2025-07-30,1,2,0,2,1,2,1.12,1,Julio Rico,Unknown,True,other
ONE-2467,- Fix new selected installments options not being reflected on api call (pull request 993),source/Modules,2025-06-12,2025-06-12,1,10,1,11,2,1,1.41,1,Rodrigo Mejia,Unknown,True,feature
ONE-2472,- Fix Checkpoint Screen for Complaints Flow (pull request 994),source/Modules,2025-06-12,2025-06-12,1,1,1,2,1,1,0.82,1,Javier Lorenzana,Unknown,True,bugfix
ONE-2499,- Fix UI discrepancies on Main Menu and Settings menu associated with ticket (pull request 1001),legacy,2025-06-19,2025-06-19,1,2,1,3,2,1,1.33,1,Javier Lorenzana,Unknown,True,bugfix
ONE-2500,- Fix reset password navigation Bug (pull request 998),source/Modules,2025-06-18,2025-06-18,1,1,1,2,1,1,0.82,1,Javier Lorenzana,Unknown,True,bugfix
ONE-2504,- Add an alert when the users attempt to save duplicated income information (pull request 1022),source/Modules,2025-07-03,2025-07-03,1,113,7,120,6,1,4.5,1,Josseh Blanco,Unknown,True,feature
ONE-2507,- Add the Address Name Type picker to the address selection form (pull request 1017),source/Modules,2025-06-26,2025-07-03,8,211,42,253,12,2,9.13,1,Josseh Blanco,Unknown,True,feature
ONE-2512,- Unintended back action on onboarding. (pull request 1021),source/Modules,2025-07-03,2025-07-03,1,84,35,119,15,1,8.99,1,Edgar Emilio Vásquez Castillo,Unknown,True,other
ONE-2514,- Fix underlined cancel text in TrustedDevices Alert (pull request 1007),source/Modules,2025-06-23,2025-06-23,1,2,2,4,2,1,1.34,1,Javier Lorenzana,Unknown,True,bugfix
ONE-2522,- Add final network connection logic proposal,source/Modules,2025-06-25,2025-07-14,20,3762,1506,5268,209,8,10.0,1,Rodrigo Mejia,Unknown,True,testing
ONE-2528,Revert - Deactivate the payment service option temporarily due to the release of version 1.18) (pull request 1053),source/Modules,2025-07-16,2025-07-16,1,4,5,9,1,3,1.49,1,Emely Melgar,Unknown,True,maintenance
ONE-2529,- Update repository to support process type.,source/Modules,2025-09-03,2025-09-03,1,45,19,64,3,1,2.44,1,Emely Melgar,Unknown,True,maintenance
ONE-2532,- Show menu alerts even if the biometric login sheet has been shown already (pull request 1012),source/Modules,2025-06-26,2025-06-26,1,11,1,12,1,1,0.92,1,Josseh Blanco,Unknown,True,other
ONE-2549,- Fix bug with disclaimer and icon in payment view (pull request 1049),source/Modules,2025-07-16,2025-07-16,1,19,9,28,7,1,4.08,1,Edgar Emilio Vásquez Castillo,Unknown,True,bugfix
ONE-2550,- Rename the placeholder for the service payment dropdown. (pull request 1023),source/Modules,2025-07-03,2025-07-03,1,1,1,2,1,1,0.82,1,Emely Melgar,Unknown,True,other
ONE-2554,- Fix NEP input invisible error label. (pull request 1038),source/Modules,2025-07-07,2025-07-07,1,1,1,2,1,1,0.82,1,Emely Melgar,Unknown,True,bugfix
ONE-2559,- Update ResetPasswordForm logic.,source/Modules,2025-09-03,2025-09-03,1,173,180,353,4,1,5.83,1,Emely Melgar,Unknown,True,maintenance
ONE-2563,- Update the placeholder for PuntoExpress forms to use the label instead of the tooltip property. (pull request 1037),source/Modules,2025-07-07,2025-07-07,1,1,1,2,1,1,0.82,1,Emely Melgar,Unknown,True,maintenance
ONE-2564,- Update convenienceProductSelection logic to select the label according to the type. (pull request 1035),source/Modules,2025-07-07,2025-07-07,1,49,18,67,3,1,2.47,1,Emely Melgar,Unknown,True,maintenance
ONE-2570,- Show the megapointscashback flow only if available points has loaded successfully (pull request 1026),source/Components,2025-07-03,2025-07-03,1,27,6,33,5,1,3.13,1,Josseh Blanco,Unknown,True,other
ONE-2576,- Fix stale income information checkpoint (pull request 1033),source/Modules,2025-07-04,2025-07-04,1,5,110,115,5,1,3.95,1,Josseh Blanco,Unknown,True,bugfix
ONE-2579,- Add new UI for delivery datetime selection (pull request 1060),source/Modules,2025-07-15,2025-07-23,9,4706,2048,6754,140,28,10.0,1,Rodrigo Mejia,Unknown,True,testing
ONE-2580,- Improve Empty States in Credit Cards section (pull request 1057),source/Modules,2025-07-23,2025-07-23,1,376,109,485,27,1,10.0,1,Julio Rico,Unknown,True,testing
ONE-2581,- Fix Protection plan screen spacing. (pull request 1040),source/Modules,2025-07-11,2025-07-11,1,17,13,30,1,1,1.1,1,Julio Rico,Unknown,True,bugfix
ONE-2585,- Add new Dynatrace events for untracked screens in Onboarding (pull request 1061),source/Modules,2025-07-22,2025-07-22,1,1103,140,1243,59,1,10.0,1,Josseh Blanco,Unknown,True,testing
ONE-2587,- Track new dynatrace events in Home and Main Menu (pull request 1086),source/Modules,2025-07-31,2025-07-31,1,745,73,818,29,1,10.0,1,Josseh Blanco,Unknown,True,testing
ONE-2589,- Track Dynatrace events in the login and credential recovery flows (pull request 1072),source/Modules,2025-07-29,2025-07-29,1,563,12,575,16,1,10.0,1,Josseh Blanco,Unknown,True,other
ONE-259,- Include the new user validation to recovery user and password.,source/Core,2025-08-29,2025-08-29,1,24,1,25,3,1,2.05,1,Emely Melgar,Unknown,True,feature
ONE-2591,- Add new dynatrace events for payments and point transfer in the card options menu (pull request 1087),source/Modules,2025-08-01,2025-08-01,1,280,2,282,10,1,8.12,1,Josseh Blanco,Unknown,True,feature
ONE-2613,- Add Progress bar on Demographics (pull request 1148),source/Modules,2025-09-19,2025-09-19,1,6,0,6,1,1,0.86,1,Javier Lorenzana,Unknown,True,feature
ONE-2618,- Remove back from survey result (pull request 1047),source/Modules,2025-07-11,2025-07-11,1,4,2,6,4,1,2.36,1,Edgar Emilio Vásquez Castillo,Unknown,True,other
ONE-2627,- Update padding spacing and scroll area in Protection Plan view. (pull request 1059),source/Modules,2025-07-15,2025-07-18,4,127,115,242,1,5,4.42,1,Julio Rico,Unknown,True,feature
ONE-2628,- Add missing back on delivery address additional card flow (pull request 1063),source/Modules,2025-07-24,2025-07-24,1,70,25,95,6,1,4.25,1,Edgar Emilio Vásquez Castillo,Unknown,True,feature
ONE-2629,- Error when entering a previously registered phone number and then replacing it with a valid one (pull request 1078),source/Modules,2025-07-24,2025-07-29,6,150,65,215,13,7,10.0,1,Javier Lorenzana,Unknown,True,other
ONE-2635,- Reset the income repository each time the user returns from onboarding to prevent duplication. (pull request 1056),source/Modules,2025-07-17,2025-07-17,1,12,0,12,2,1,1.42,1,Emely Melgar,Unknown,True,other
ONE-2637,- Update generic ups alert to support dismiss and custom action. (pull request 1076),source/Modules,2025-07-29,2025-07-29,1,102,16,118,4,1,3.48,1,Emely Melgar,Unknown,True,maintenance
ONE-2729,- Fix bug with the sign (pull request 1058),source/Modules,2025-07-18,2025-07-18,1,1,1,2,1,1,0.82,1,Edgar Emilio Vásquez Castillo,Unknown,True,bugfix
ONE-2732,- Fraud protection empty state UX improvement (pull request 1088),source/Modules,2025-08-01,2025-08-01,1,243,210,453,17,1,10.0,1,Edgar Emilio Vásquez Castillo,Unknown,True,testing
ONE-2733,- Add new cashback UXUI (pull request 1089),source/Modules,2025-08-07,2025-08-07,1,1219,128,1347,32,1,10.0,1,Rodrigo Mejia,Unknown,True,feature
ONE-2755,- Update Endpoint name to validate-forbidden-words (pull request 1100),source/Core,2025-08-13,2025-08-13,1,6,14,20,1,2,1.3,1,José Miguel Rivera López,Unknown,True,maintenance
ONE-2760,- Fix navigation back on Document Validation in General Data. (pull request 1115),source/Modules,2025-08-27,2025-08-27,1,3,1,4,1,1,0.84,1,Javier Lorenzana,Unknown,True,bugfix
ONE-2767,Merged in feature(pull request 1064),source/Modules,2025-07-24,2025-07-24,1,41,1252,1293,66,1,10.0,1,Emely Melgar,Unknown,True,testing
ONE-2768,- Add last files,tests,2025-07-24,2025-08-07,15,215,181,396,24,6,10.0,2,"Emely Melgar, Javier Lorenzana",Unknown,True,testing
ONE-2770,Weight Reduction - Optimize assets (Change R.swift Localized Strings to native ones) (pull request 1106),source/Modules,2025-09-03,2025-09-03,1,2127,328,2455,75,1,10.0,1,José Miguel Rivera López,Unknown,True,refactor
ONE-2771,- Remove commented files and move remaining legacy files (15),source/Core,2025-10-03,2025-10-03,1,17,529,546,20,1,10.0,1,remejia-applaudo,Unknown,True,testing
ONE-2774,- Fix the logout request by updating the user session provider to send the authorization token. (pull request 1068),source/Core,2025-07-24,2025-07-24,1,8,5,13,2,1,1.43,1,Emely Melgar,Unknown,True,bugfix
ONE-2779,- Fix hide back button logic in main ticket view (pull request 1070),source/Modules,2025-07-25,2025-07-25,1,4,1,5,1,1,0.85,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-2797,- Fix missing UI update on additional cardthird attempt flows (pull request 1079),source/Modules,2025-07-25,2025-07-30,6,607,123,730,14,5,10.0,1,Rodrigo Mejia,Unknown,True,feature
ONE-2801,- Fix typo in empty state copy. (pull request 1073),source/Modules,2025-07-29,2025-07-29,1,63,1,64,3,1,2.44,1,Julio Rico,Unknown,True,bugfix
ONE-2821,- Add missing Installment Value field in TicketContentView (PayInInstallments Flow) (pull request 1099),source/Modules,2025-08-12,2025-08-13,2,70,10,80,3,4,3.5,1,Rodrigo Mejia,Unknown,True,feature
ONE-2827,- Include new target APIVersion property to support mulesoft and APIGateway url. (pull request 1107),source/Core,2025-08-25,2025-08-25,1,209,55,264,25,1,10.0,1,Emely Melgar,Unknown,True,testing
ONE-2828,- Add new Selfie Scan Tutorials (pull request 1113),source/DesignSystem,2025-08-25,2025-08-25,1,1403,148,1551,79,1,10.0,1,Josseh Blanco,Unknown,True,testing
ONE-2837,- Fix empty states for Card Pin. (pull request 1085),source/Core,2025-07-31,2025-07-31,1,10,1,11,2,1,1.41,1,Julio Rico,Unknown,True,bugfix
ONE-2855,- Fix management status picker reverting back to active after viewing a management detail (pull request 1091),source/Modules,2025-08-07,2025-08-07,1,49,20,69,3,1,2.49,1,Josseh Blanco,Unknown,True,bugfix
ONE-2860,- Use new MenuAlertsV2 and refactor notification presentation logic (pull request 1134),source/Modules,2025-09-10,2025-09-10,1,596,373,969,28,1,10.0,1,Rodrigo Mejia,Unknown,True,testing
ONE-2864,- Fix no DUI Warning bubble position (pull request 1117),source/Modules,2025-08-27,2025-08-27,1,2,1,3,1,1,0.83,1,José Miguel Rivera López,Unknown,True,bugfix
ONE-2865,- Fix textfield validation for additional card document form. (pull request 1090),source/Modules,2025-08-07,2025-08-07,1,3,1,4,2,1,1.34,1,Emely Melgar,Unknown,True,feature
ONE-2873,- Remove back button for disable confirmation screen. (pull request 1092),source/Modules,2025-08-07,2025-08-07,1,8,1,9,2,1,1.39,1,Emely Melgar,Unknown,True,other
ONE-2880,- Update the empty state button text for protection plan screen. (pull request 1093),source/Modules,2025-08-07,2025-08-07,1,12,1,13,2,1,1.43,1,Emely Melgar,Unknown,True,maintenance
ONE-2883,- Fix DateFormat in Rejection case (pull request 1104),source/Core,2025-08-20,2025-08-20,1,11,3,14,2,1,1.44,1,Javier Lorenzana,Unknown,True,bugfix
ONE-2887,- Replace the one app icon with a icon new version. (pull request 1110),source/DesignSystem,2025-08-21,2025-08-21,1,58,97,155,42,1,10.0,1,Emely Melgar,Unknown,True,feature
ONE-2899,- Add improvements for SonarQube report (pull request 1135),ci_cd,2025-09-11,2025-09-11,1,24,12,36,3,1,2.16,1,Edgar Emilio Vásquez Castillo,Unknown,True,feature
ONE-2901,- Include row builder.,source/Modules,2025-09-22,2025-09-23,2,290,30,320,8,3,8.1,1,Emely Melgar,Unknown,True,other
ONE-2907,"- Remove close icon on Alert, add dot (.) at the end of the text (pull request 1101)",source/Modules,2025-08-19,2025-08-19,1,2,2,4,2,1,1.34,1,Javier Lorenzana,Unknown,True,feature
ONE-2916,- Update the force update logic including the bundle version and build number. (pull request 1109),source/Core,2025-08-21,2025-08-21,1,246,47,293,9,1,7.73,1,Emely Melgar,Unknown,True,testing
ONE-2919,- Fix service validate-forbidden-word error handling (pull request 1112),source/Components,2025-09-01,2025-09-01,1,30,5,35,2,1,1.65,1,José Miguel Rivera López,Unknown,True,bugfix
ONE-2922,- Implemente the state Z for users with overdue payments and hides the payment option in the card menu. (pull request 1105),source/Modules,2025-08-19,2025-08-19,1,92,15,107,4,1,3.37,1,Emely Melgar,Unknown,True,feature
ONE-2944,- Show biometry activation sheet after successful password update (pull request 1126),source/Components,2025-09-01,2025-09-01,1,14,5,19,1,1,0.99,1,Julio Rico,Unknown,True,maintenance
ONE-2955,- Include app version for non productive environments. (pull request 1114),source/Modules,2025-08-22,2025-08-22,1,52,21,73,9,1,5.53,1,Emely Melgar,Unknown,True,testing
ONE-2958,- Fix card delivery datetime Selectables UI (pull request 1116),source/Components,2025-08-25,2025-08-25,1,101,18,119,5,1,3.99,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-2972,- Fix historical Automatic Payment display issue on view first appear. (pull request 1121),source/Modules,2025-08-28,2025-08-28,1,5,2,7,1,1,0.87,1,Julio Rico,Unknown,True,bugfix
ONE-2973,- Fix historical Automatic Payment when applying date filters. (pull request 1122),source/Modules,2025-08-28,2025-08-28,1,0,9,9,1,1,0.89,1,Julio Rico,Unknown,True,bugfix
ONE-2978,Weight Reduction - Optimize Assets (Create Subsets for Fonts) (pull request 1136),source/DesignSystem,2025-09-10,2025-09-10,1,0,111,111,39,1,10.0,1,José Miguel Rivera López,Unknown,True,refactor
ONE-2980,- Move the IdentificationImages endpoint after ccapplication request. (pull request 1125),source/Core,2025-08-29,2025-08-29,1,401,341,742,14,1,10.0,1,Emely Melgar,Unknown,True,testing
ONE-2981,- Modify Credit card Text in AutoPayContractDetailView (pull request 1120),source/Modules,2025-08-27,2025-08-27,1,1,1,2,1,1,0.82,1,Javier Lorenzana,Unknown,True,other
ONE-2986,- Remove unnecessary flag checker (pull request 1123),source/Modules,2025-08-28,2025-08-28,1,4,1,5,1,1,0.85,1,Edgar Emilio Vásquez Castillo,Unknown,True,other
ONE-2987,- Remove unnecessary X button from the biometry blocked alert (pull request 1124),source/Modules,2025-08-28,2025-08-28,1,1,1,2,1,1,0.82,1,Josseh Blanco,Unknown,True,other
ONE-2991,- Modify text to match with Design on Figma (16),source/Modules,2025-10-03,2025-10-03,1,2,2,4,1,1,0.84,1,jlorenzanaBancatlan,Unknown,True,other
ONE-2993,- Refactor selfie template to handle different and future reason.(pull request 1127),source/Modules,2025-09-02,2025-09-02,1,207,83,290,9,1,7.7,1,Emely Melgar,Unknown,True,refactor
ONE-2995,- Fix the try quality and try face alerts bullet format in the identity validation checkpoint (pull request 1138),source/Modules,2025-09-11,2025-09-11,1,2,2,4,1,1,0.84,1,Josseh Blanco,Unknown,True,bugfix
ONE-3016,- Keep tab bar visible in Main Menu and Credit Card Menu (pull request 1140) (pull request 1140),source/Modules,2025-09-18,2025-09-18,1,64,28,92,6,1,4.22,1,Julio Rico,Unknown,True,other
ONE-3017,"- Include Dynatrace events for CreditCardMenu, CardOptions and CardPin features. (pull request 1141)",source/Modules,2025-09-16,2025-09-16,1,947,10,957,16,1,10.0,1,Emely Melgar,Unknown,True,testing
ONE-3018,- Update new logo icon (pull request 1139),source/Modules,2025-09-12,2025-09-23,12,2211,833,3044,144,8,10.0,3,"Javier Lorenzana, José Miguel Rivera López, Miguel Rivera",Unknown,True,testing
ONE-3019,- Fix handler error replacing Embeddable for OptionalEmbeddable.(pull request 1131),tests,2025-09-04,2025-09-04,1,2,2,4,2,1,1.34,1,Emely Melgar,Unknown,True,testing
ONE-3020,- Add Overdue Payments account Status empty states and alert (pull request 1150),source/Modules,2025-09-17,2025-09-24,8,3316,738,4054,120,20,10.0,1,Rodrigo Mejia,Unknown,True,testing
ONE-3025,- Move to guest view temporarily. (pull request 1133),source/Modules,2025-09-05,2025-09-05,1,1,1,2,1,1,0.82,1,Emely Melgar,Unknown,True,other
ONE-3030,- Modify delivery disclaimer text (pull request 1143),source/Modules,2025-09-16,2025-09-16,1,3,3,6,3,1,1.86,1,Javier Lorenzana,Unknown,True,other
ONE-3031,- Update the selfie scan reason copies (pull request 1147),source/Modules,2025-09-19,2025-09-19,1,308,81,389,7,1,7.69,1,Josseh Blanco,Unknown,True,testing
ONE-3036,- Add new popToFirst function and move the users to the document scan view when identification image fails (1),source/Modules,2025-09-25,2025-09-25,1,145,20,165,7,1,5.45,1,Josseh Blanco,Unknown,True,feature
ONE-3058,- Upload XML report dependency check,other,2025-09-23,2025-09-23,1,51,1,52,4,2,3.12,2,"Edgar Emilio Vásquez Castillo, Emilio Vasquez",Unknown,True,maintenance
ONE-3102,- Update email label for the automatic services payment detail. (17),source/Core,2025-10-06,2025-10-06,1,1,1,2,1,1,0.82,1,Emely Melgar,Unknown,True,maintenance
ONE-3125,- Prevent MenuAlerts failure to stop login (pull request 1142),source/Modules,2025-09-16,2025-09-16,1,27,12,39,2,1,1.69,1,Rodrigo Mejia,Unknown,True,other
ONE-3138,- Fix double divider and empty space on card menu. (pull request 1146),source/Modules,2025-09-17,2025-09-23,7,42,45,87,2,12,5.47,1,Javier Lorenzana,Unknown,True,bugfix
ONE-3156,- Update the document scan tutorial design (9),source/DesignSystem,2025-09-29,2025-09-29,1,200,225,425,19,1,10.0,1,Josseh Blanco,Unknown,True,maintenance
ONE-3158,Merge remote-tracking branch origindevelop into feature,legacy,2025-10-01,2025-10-06,6,2012,2930,4942,195,3,10.0,1,Miguel Rivera,Unknown,True,testing
ONE-3164,- Group ticket detail files. (pull request 1151),source/Modules,2025-09-19,2025-09-19,1,92,78,170,11,1,7.5,1,Emely Melgar,Unknown,True,other
ONE-3167,- Fix parsing of Purchase Amount property in ticket detail domain (pull request 1154),legacy,2025-09-24,2025-09-24,1,7,7,14,3,1,1.94,1,Rodrigo Mejia,Unknown,True,bugfix
ONE-3169,- Apply new design on select names section (6),source/Modules,2025-09-24,2025-09-30,7,774,98,872,44,9,10.0,2,"Javier Lorenzana, jlorenzanaBancatlan",Unknown,True,testing
ONE-3170,- Add some missing default errors,source/Components,2025-09-25,2025-10-03,9,1552,184,1736,55,11,10.0,1,Emilio Vasquez,Unknown,True,testing
ONE-3188,- Rename OneCardPinCheckpointHandler to OneCardPinCheckpointInteractor.,source/Modules,2025-10-01,2025-10-06,6,584,221,805,40,24,10.0,1,Julio Rico,Unknown,True,testing
ONE-3189,Merge branch develop into feature,source/Modules,2025-09-30,2025-10-06,7,2424,3121,5545,251,18,10.0,1,Josseh Blanco,Unknown,True,testing
ONE-3194,- Update path and response with new MenuAlerts endpoint spec (3),source/Core,2025-09-24,2025-09-24,1,33,1,34,2,1,1.64,1,remejia-applaudo,Unknown,True,feature
ONE-3224,- Update MainCarousel behaviour and include a CustomPageIndicator. (12),source/Components,2025-10-01,2025-10-01,1,537,122,659,12,1,10.0,1,Emely Melgar,Unknown,True,maintenance
ONE-3226,- Remove unused files and mark used legacy files (11),legacy,2025-10-01,2025-10-01,1,188,914,1102,67,1,10.0,1,remejia-applaudo,Unknown,True,other
ONE-3227,- Remove and relocate legacy files (Pt. 2) (14),legacy,2025-10-02,2025-10-02,1,610,1301,1911,99,1,10.0,1,remejia-applaudo,Unknown,True,testing
ONE-3259,- Resize close button and prevent scroll in Overdue Payments Sheet (18),source/Modules,2025-10-06,2025-10-06,1,4,4,8,1,2,1.18,2,"Rodrigo Mejia, remejia-applaudo",Unknown,True,other
ONE-507,- Implement Empty State UI for Card Options and Card Pin Change,source/Modules,2024-12-03,2024-12-03,1,677,233,910,18,1,10.0,1,Vladimir Guevara,Unknown,True,feature
ONE-509,- Builds Adress Selection Form Interactor,source/Modules,2024-11-11,2024-11-11,1,258,143,401,19,1,10.0,1,Vladimir Guevara,Unknown,True,other
ONE-538,- Biometric Log in on fresh install (pull request 696),source/Modules,2024-11-05,2024-11-05,1,1600,168,1768,40,1,10.0,1,Julio Rico,Unknown,True,testing
ONE-648,- Onboarding - Financial products with other banks (pull request 679),source/Modules,2024-10-17,2024-10-17,1,953,415,1368,39,1,10.0,1,Julio Rico,Unknown,True,testing
ONE-858,- Hide expenses tab from tabview (pull request 925),legacy,2025-04-29,2025-04-29,1,5,5,10,1,1,0.9,1,Rodrigo Mejia,Unknown,True,other
ONE-862,- Add missing disclaimer to workplace view (pull request 702),source/Modules,2024-11-04,2024-11-04,1,29,9,38,2,1,1.68,1,Rodrigo Mejia,Unknown,True,feature
ONE-870,- Create new auto validating textfield component and add it to customer workplace form (pull request 678),source/Components,2024-10-15,2024-10-15,1,603,44,647,22,1,10.0,1,Rodrigo Mejia,Unknown,True,feature
ONE-902,- Add localization to Home section (pull request 741),source/Modules,2024-12-04,2024-12-04,1,671,64,735,18,1,10.0,1,Julio Rico,Unknown,True,feature
ONE-903,- Add localization to Credit Cards (pull request 746),source/Modules,2024-12-11,2024-12-11,1,595,62,657,16,1,10.0,1,Julio Rico,Unknown,True,feature
ONE-904,- Update text values for menu section (pull request 778),source/Modules,2025-01-08,2025-01-08,1,551,54,605,14,1,10.0,1,Julio Rico,Unknown,True,maintenance
ONE-909,- Add Localizations to Demographic Section (pull request 674),source/Modules,2024-10-08,2024-10-08,1,343,28,371,28,1,10.0,1,Julio Rico,Unknown,True,feature
ONE-911,- Change voice intonation an alert types in LoginPageAlerts,source/Modules,2025-01-03,2025-01-03,1,128,17,145,4,1,3.75,1,Vladimir Guevara,Unknown,True,other
ONE-973,- Add address names in Onboarding,source/Modules,2024-10-17,2024-10-17,1,326,102,428,11,1,10.0,1,Vladimir Guevara,Unknown,True,testing
ONE-976,- Add address names for card managements,source/Modules,2024-10-18,2024-10-18,1,1,1,2,1,1,0.82,1,Vladimir Guevara,Unknown,True,feature
ONE-977,- Use auto validated phone number field on personal data flows (pull request 692),source/Modules,2024-10-30,2024-10-30,1,318,233,551,18,1,10.0,1,Rodrigo Mejia,Unknown,True,other
ONE-981,- Add biometric validation to the cancellation card flow. (pull request 681),source/Modules,2024-10-17,2024-10-17,1,1175,562,1737,39,1,10.0,1,Emely Melgar,Unknown,True,testing
ONE-983,- Include workaround to support long press gesture. (pull request 697),source/Components,2024-10-16,2024-10-28,13,1599,244,1843,43,2,10.0,1,Emely Melgar,Unknown,True,other
ONE-985,Merged in feature(pull request 693),source/DesignSystem,2024-10-30,2024-10-30,1,102,53,155,4,1,3.85,1,Vladimir Guevara,Unknown,True,feature
ONE-988,- Change voice intonation for short capture and creates a new string catalog (pull request 663),source/Modules,2024-10-07,2024-10-07,1,815,57,872,56,1,10.0,1,Vladimir Guevara,Unknown,True,feature
PATTERN-0340,Multiple Controls of the same type in a single view. Fix Cancel option Fix Validation of controls Fix missed form structure when the service of complaints response code 200 add Tracking view add Red v,ios_code,2022-03-03,2022-03-03,1,876,79,955,20,1,10.0,1,Jaime Mejia,Unknown,False,feature
PATTERN-0341,Merge branch develop into featuretwilio_flex,ios_code,2022-08-18,2022-08-18,1,6321,2479,8800,232,1,10.0,1,Cesar Callejas,Unknown,False,feature
PATTERN-0342,Merged PR 1244 fix,ios_code,2022-08-18,2022-08-18,1,147,108,255,6,1,5.85,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0343,106- No permite agregar una dirección adicional en la sección de Mis Datos Personales,ios_code,2022-08-18,2022-08-18,1,147,108,255,6,1,5.85,1,Jaime Mejia,Unknown,False,other
PATTERN-0344,Merged PR 1243 fix,ios_code,2022-08-17,2022-08-17,1,90,94,184,15,1,9.64,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0345,112- Corregir doble back de desactivación de programa de protección,ios_code,2022-08-17,2022-08-17,1,90,94,184,15,1,9.64,1,Jaime Mejia,Unknown,False,other
PATTERN-0346,Merged PR 1242 fix,ios_code,2022-08-17,2022-08-17,1,185,92,277,11,1,8.57,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0347,101- Ajuste de UI en pantalla de Tarjeta de identificación 107- Falta tilde en la pantalla de información laboral 105- Al mensaje de inactividad le hace falta una tilde,ios_code,2022-08-17,2022-08-17,1,185,92,277,11,1,8.57,1,Jaime Mejia,Unknown,False,other
PATTERN-0348,default color selection for card color,ios_code,2022-02-21,2022-02-21,1,15,1,16,1,1,0.96,1,Benjamin Rivera,Unknown,False,other
PATTERN-0349,Merged PR 576 Merge,ios_code,2022-02-21,2022-02-21,1,30,120,150,4,1,3.8,1,César Callejas,Unknown,False,other
PATTERN-0350,scroll screen,ios_code,2022-02-21,2022-02-21,1,30,120,150,4,1,3.8,1,Benjamin Rivera,Unknown,False,other
PATTERN-0351,flujo onboarding,resources,2022-02-20,2022-02-20,1,1632,7479,9111,182,1,10.0,1,Benjamin Rivera,Unknown,False,other
PATTERN-0352,read dui again,ios_code,2022-02-18,2022-02-18,1,29,15,44,3,1,2.24,1,Benjamin Rivera,Unknown,False,other
PATTERN-0353,,ios_code,2022-02-18,2022-02-18,1,74,35,109,4,1,3.39,1,Benjamin Rivera,Unknown,False,other
PATTERN-0354,Merged PR 2391 bug cards options and payment card,ios_code,2023-04-26,2023-04-26,1,42,29,71,6,1,4.01,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0355,bug cards options and payment card,ios_code,2023-04-26,2023-04-26,1,42,29,71,6,1,4.01,1,bryan garcia,Unknown,False,bugfix
PATTERN-0356,Merged PR 2390 bug Navigation on add additional cards fixed,ios_code,2023-04-26,2023-04-26,1,149,104,253,7,1,6.33,1,Bryan Alexis García García,Unknown,False,feature
PATTERN-0357,bug correct color id sent on additional card,ios_code,2023-04-25,2023-04-25,1,18,17,35,2,1,1.65,1,bryan garcia,Unknown,False,feature
PATTERN-0358,bug Navigation on add additional cards fixed,ios_code,2023-04-25,2023-04-25,1,132,88,220,6,1,5.5,1,bryan garcia,Unknown,False,feature
PATTERN-0359,Merged PR 2389 bug back issues resolve,ios_code,2023-04-25,2023-04-25,1,18,21,39,4,1,2.69,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0360,bug remove comments,ios_code,2023-04-25,2023-04-25,1,0,96,96,1,1,1.76,1,bryan garcia,Unknown,False,bugfix
PATTERN-0361,bug back issues resolve,ios_code,2023-04-25,2023-04-25,1,114,21,135,4,1,3.65,1,bryan garcia,Unknown,False,bugfix
PATTERN-0362,Merged PR 2388 bug password reset solved,ios_code,2023-04-24,2023-04-24,1,90,24,114,7,1,4.94,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0363,bug password reset solved,ios_code,2023-04-24,2023-04-24,1,90,24,114,7,1,4.94,1,bryan garcia,Unknown,False,bugfix
PATTERN-0364,Merged PR 2387 multiples bugs,ios_code,2023-04-24,2023-04-24,1,4,3,7,2,1,1.37,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0365,bug multiples bugs on the app,ios_code,2023-04-24,2023-04-24,1,3,2,5,2,1,1.35,1,bryan garcia,Unknown,False,bugfix
PATTERN-0366,bug user need to can not continues unless select up to two items on names scroll,ios_code,2023-04-21,2023-04-21,1,10,7,17,4,1,2.47,1,bryan garcia,Unknown,False,bugfix
PATTERN-0367,bug updates,ios_code,2023-04-20,2023-04-20,1,36,99,135,5,1,4.15,1,bryan garcia,Unknown,False,bugfix
PATTERN-0368,Merged PR 2376 bug cards payment fixed,ios_code,2023-04-18,2023-04-18,1,9,3,12,1,1,0.92,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0369,bug cards payment fixed,ios_code,2023-04-18,2023-04-18,1,9,3,12,1,1,0.92,1,bryan garcia,Unknown,False,bugfix
PATTERN-0370,Merged PR 2374 bug cards payment fixed,ios_code,2023-04-18,2023-04-18,1,21,5,26,4,1,2.56,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0371,bug cards payment fixed,ios_code,2023-04-18,2023-04-18,1,21,5,26,4,1,2.56,1,bryan garcia,Unknown,False,bugfix
PATTERN-0372,Merged PR 2373 bug cards payment fixed,ios_code,2023-04-18,2023-04-18,1,19,4,23,4,1,2.53,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0373,bug cards payment fixed,ios_code,2023-04-17,2023-04-17,1,19,4,23,4,1,2.53,1,bryan garcia,Unknown,False,bugfix
PATTERN-0374,Merged PR 2372 bug cards payment fixed,ios_code,2023-04-18,2023-04-18,1,14,4,18,2,1,1.48,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0375,bug cards payment fixed,ios_code,2023-04-17,2023-04-17,1,14,4,18,2,1,1.48,1,bryan garcia,Unknown,False,bugfix
PATTERN-0376,Merged PR 2371 bug cards payment,ios_code,2023-04-17,2023-04-17,1,18,8,26,2,1,1.56,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0377,bug cards payment,ios_code,2023-04-17,2023-04-17,1,18,8,26,2,1,1.56,1,bryan garcia,Unknown,False,bugfix
PATTERN-0378,bug pago a tarjetas,ios_code,2023-04-17,2023-04-17,1,18,8,26,2,1,1.56,1,bryan garcia,Unknown,False,bugfix
PATTERN-0379,"Merged PR 2366 OBS 26 - Verificar Carga de Gestiones, en ocasiones no se muestran",ios_code,2023-04-17,2023-04-17,1,27,11,38,2,1,1.68,1,Bryan Alexis García García,Unknown,False,other
PATTERN-0380,Merged PR 2362 OBS 04 completed,project_config,2023-04-14,2023-04-14,1,75,45,120,17,1,10.0,1,Bryan Alexis García García,Unknown,False,testing
PATTERN-0381,bug OBS 04 COMPLETED,ios_code,2023-04-14,2023-04-14,1,12,85,97,4,1,3.27,1,bryan garcia,Unknown,False,bugfix
PATTERN-0382,bug updated,project_config,2023-04-14,2023-04-14,1,40,4,44,1,1,1.24,1,bryan garcia,Unknown,False,bugfix
PATTERN-0383,Merged PR 2355 bug(ChatView) Fix twilio chat with colors of bubble,ios_code,2023-04-14,2023-04-14,1,124,144,268,6,1,5.98,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0384,bug(ChatView) Fix twilio chat with colors of bubble,ios_code,2023-04-13,2023-04-13,1,8,39,47,1,1,1.27,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0385,Merged PR 2352 bug string extension,ios_code,2023-04-13,2023-04-13,1,8,10,18,1,1,0.98,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0386,bug string extension,ios_code,2023-04-13,2023-04-13,1,8,10,18,1,1,0.98,1,bryan garcia,Unknown,False,bugfix
PATTERN-0387,Merged PR 2351 active button on credit card payment option,ios_code,2023-04-13,2023-04-13,1,12,1,13,2,1,1.43,1,Bryan Alexis García García,Unknown,False,other
PATTERN-0388,bug covert from money string value to double as a extension of string,ios_code,2023-04-13,2023-04-13,1,11,0,11,1,1,0.91,1,bryan garcia,Unknown,False,bugfix
PATTERN-0389,Merged PR 2348 bug adjust filter on card payment options,ios_code,2023-04-12,2023-04-12,1,36,2,38,3,1,2.18,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0390,bug adjust filter on card payment options,ios_code,2023-04-12,2023-04-12,1,36,2,38,3,1,2.18,1,bryan garcia,Unknown,False,bugfix
PATTERN-0391,Merged PR 2340 misc fixes,other,2023-04-11,2023-04-11,1,21720,20570,42290,1325,1,10.0,1,Jaime Arévalo,Unknown,False,testing
PATTERN-0392,bug(around onboarding) fix issue in step 7 and navigation when return to ConsultaDuiView,ios_code,2023-04-11,2023-04-11,1,207,34,241,12,1,8.71,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0393,Merged PR 2329 bug fixed on the app,ios_code,2023-04-06,2023-04-06,1,10,14,24,4,1,2.54,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0394,bug fixed on the app,ios_code,2023-04-06,2023-04-06,1,10,14,24,4,1,2.54,1,bryan garcia,Unknown,False,bugfix
PATTERN-0395,Merged PR 2328 bug(onboarding) Fix misc issues in onboarding,ios_code,2023-04-06,2023-04-06,1,129,135,264,7,1,6.44,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0396,bug(onboarding) Fix misc issues in onboarding,ios_code,2023-04-06,2023-04-06,1,129,135,264,7,1,6.44,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0397,"Merged PR 2311 bug(EndpointFraudProtectionGet,ProtectionsPageViewModel) fix request format",other,2023-03-31,2023-03-31,1,53,13,66,7,1,4.46,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0398,"bug(EndpointFraudProtectionGet,ProtectionsPageViewModel) fix request format",other,2023-03-31,2023-03-31,1,53,13,66,7,1,4.46,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0399,Merged PR 2306 bug Fix in multiple screes because the merge,ios_code,2023-03-31,2023-03-31,1,140,190,330,11,1,9.1,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0400,bug Fix in multiple screes because the merge,ios_code,2023-03-31,2023-03-31,1,140,190,330,11,1,9.1,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0401,bug Fix in biometric login and complaints,ios_code,2023-03-31,2023-03-31,1,95,152,247,12,1,8.77,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0402,Merged PR 2298 bug navigation at home,ios_code,2023-03-30,2023-03-30,1,6,5,11,1,1,0.91,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0403,bug navigation at home,ios_code,2023-03-30,2023-03-30,1,6,5,11,1,1,0.91,1,bryan garcia,Unknown,False,bugfix
PATTERN-0404,Merged PR 2284 bug Fix misc,ios_code,2023-03-29,2023-03-29,1,123,238,361,14,1,10.0,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0405,bug Fix misc,ios_code,2023-03-29,2023-03-29,1,86,197,283,11,1,8.63,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0406,Merged PR 2281 bug(ReferenciaPersonalPersonal1View) Fix issue with header,ios_code,2023-03-29,2023-03-29,1,104,102,206,1,1,2.86,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0407,bug(ReferenciaPersonalPersonal1View) Fix issue with header,ios_code,2023-03-29,2023-03-29,1,104,102,206,1,1,2.86,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0408,Merged PR 2280 bug Navigation bugs,ios_code,2023-03-29,2023-03-29,1,18,22,40,5,1,3.2,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0409,bug Navigation bugs,ios_code,2023-03-29,2023-03-29,1,18,22,40,5,1,3.2,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0410,Merged PR 2261 Fix in my moves,ios_code,2023-03-28,2023-03-28,1,80,113,193,7,1,5.73,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0411,"Merged PR 2260 bug(My moves workflow) button back in ticket doesnt work, and alert in bl...",ios_code,2023-03-28,2023-03-28,1,54,118,172,7,1,5.52,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0412,"bug(My moves workflow) button back in ticket doesnt work, and alert in blank was fixed replacing with",ios_code,2023-03-27,2023-03-27,1,54,118,172,7,1,5.52,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0413,Merged PR 2258 Fix in my moves,ios_code,2023-03-27,2023-03-27,1,6,3,9,2,1,1.39,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0414,Merged PR 2257 bug Fix in my moves,ios_code,2023-03-27,2023-03-27,1,6,3,9,2,1,1.39,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0415,bug Fix in my moves,ios_code,2023-03-27,2023-03-27,1,6,3,9,2,1,1.39,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0416,Merged PR 2249 Misc changes,ios_code,2023-03-24,2023-03-24,1,82,70,152,6,1,4.82,1,Jaime Arévalo,Unknown,False,other
PATTERN-0417,Merged PR 2247 new Navigation,ios_code,2023-03-24,2023-03-24,1,2623,2272,4895,148,1,10.0,1,Jaime Arévalo,Unknown,False,feature
PATTERN-0418,Merged PR 2239 bug style menu Page,ios_code,2023-03-22,2023-03-22,1,3,3,6,1,1,0.86,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0419,bug style menu Page,ios_code,2023-03-22,2023-03-22,1,3,3,6,1,1,0.86,1,bryan garcia,Unknown,False,bugfix
PATTERN-0420,Merged PR 2238 bug menu style,ios_code,2023-03-22,2023-03-22,1,3,3,6,1,1,0.86,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0421,bug menu style,ios_code,2023-03-22,2023-03-22,1,3,3,6,1,1,0.86,1,bryan garcia,Unknown,False,bugfix
PATTERN-0422,Merged PR 2235 bug calls NewCards issues,ios_code,2023-03-22,2023-03-22,1,113,48,161,9,1,6.41,1,Bryan Alexis García García,Unknown,False,feature
PATTERN-0423,Merged PR 2237 bug CustomCard Issues,ios_code,2023-03-22,2023-03-22,1,14,0,14,2,1,1.44,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0424,bug CustomCard Issues,ios_code,2023-03-22,2023-03-22,1,14,0,14,2,1,1.44,1,bryan garcia,Unknown,False,bugfix
PATTERN-0425,bug calls NewCards issues,ios_code,2023-03-22,2023-03-22,1,13,13,26,4,1,2.56,1,bryan garcia,Unknown,False,feature
PATTERN-0426,Merged PR 2233 bug navigationStep 8,ios_code,2023-03-22,2023-03-22,1,8,6,14,1,1,0.94,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0427,bug navigationStep 8,ios_code,2023-03-22,2023-03-22,1,8,6,14,1,1,0.94,1,bryan garcia,Unknown,False,bugfix
PATTERN-0428,Merged PR 2228 navigation cards,project_config,2023-03-22,2023-03-22,1,38,38,76,2,1,2.06,1,Bryan Alexis García García,Unknown,False,other
PATTERN-0429,Merged PR 2227 bug navigation cards,project_config,2023-03-22,2023-03-22,1,37,37,74,2,1,2.04,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0430,bug navigation cards,project_config,2023-03-22,2023-03-22,1,37,37,74,2,1,2.04,1,bryan garcia,Unknown,False,bugfix
PATTERN-0431,Merged PR 2225 bugnavigation from card,ios_code,2023-03-22,2023-03-22,1,10,4,14,1,1,0.94,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0432,Merged PR 2226 bug alert navigation,ios_code,2023-03-22,2023-03-22,1,10,4,14,1,1,0.94,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0433,bug alert navigation,ios_code,2023-03-22,2023-03-22,1,10,4,14,1,1,0.94,1,bryan garcia,Unknown,False,bugfix
PATTERN-0434,bugnavigation from card,ios_code,2023-03-22,2023-03-22,1,10,4,14,1,1,0.94,1,bryan garcia,Unknown,False,bugfix
PATTERN-0435,"bug(HomeRouter,SolicitudTarjetaSelectHourViewCardBlock) Fix navigation",ios_code,2023-03-22,2023-03-22,1,5,5,10,2,1,1.4,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0436,Merged PR 2203 alert beneficios,ios_code,2023-03-17,2023-03-17,1,18,7,25,4,1,2.55,1,Bryan Alexis García García,Unknown,False,other
PATTERN-0437,Merged PR 2202 bug alert beneficios,ios_code,2023-03-17,2023-03-17,1,18,7,25,4,1,2.55,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0438,bug alert beneficios,ios_code,2023-03-17,2023-03-17,1,18,7,25,4,1,2.55,1,bryan garcia,Unknown,False,bugfix
PATTERN-0439,Merged PR 2199 crash Chat twilio,ios_code,2023-03-17,2023-03-17,1,44,39,83,3,1,2.63,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0440,Merged PR 2200 bug crash Chat twill,ios_code,2023-03-17,2023-03-17,1,42,39,81,2,1,2.11,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0441,bug crash Chat twill,ios_code,2023-03-17,2023-03-17,1,42,39,81,2,1,2.11,1,bryan garcia,Unknown,False,bugfix
PATTERN-0442,Merged PR 2195 bug names on congrats view has been fixed,ios_code,2023-03-16,2023-03-16,1,103,74,177,2,1,3.07,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0443,bug names on congrats view has been fixed,ios_code,2023-03-16,2023-03-16,1,103,74,177,2,1,3.07,1,bryan garcia,Unknown,False,bugfix
PATTERN-0444,Merged PR 2194 congrats View name issues,ios_code,2023-03-16,2023-03-16,1,103,75,178,2,1,3.08,1,Bryan Alexis García García,Unknown,False,other
PATTERN-0445,bug congrats View name issues,ios_code,2023-03-16,2023-03-16,1,51,38,89,1,1,1.69,1,bryan garcia,Unknown,False,bugfix
PATTERN-0446,bug name on congratulation View,ios_code,2023-03-16,2023-03-16,1,52,37,89,1,1,1.69,1,bryan garcia,Unknown,False,bugfix
PATTERN-0447,Merged PR 2175 bug(AfpValidationViewModel) Fix issue with Firm,ios_code,2023-03-15,2023-03-15,1,25,2,27,1,1,1.07,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0448,bug(AfpValidationViewModel) Fix issue with Firm,ios_code,2023-03-15,2023-03-15,1,25,2,27,1,1,1.07,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0449,Merged PR 2172 bug(AddressDetailes2ViewModel) Fix catch wrong model Id to populate the dr...,ios_code,2023-03-15,2023-03-15,1,4,4,8,1,1,0.88,1,Jaime Arévalo,Unknown,False,feature
PATTERN-0450,Merged PR 2171 bug(AddressDetailes2ViewModel) Fix catch wrong model Id to populate the dr...,ios_code,2023-03-15,2023-03-15,1,4,4,8,1,1,0.88,1,Jaime Arévalo,Unknown,False,feature
PATTERN-0451,bug(AddressDetailes2ViewModel) Fix catch wrong model Id to populate the dropdown of municipality,ios_code,2023-03-14,2023-03-14,1,4,4,8,1,1,0.88,1,Jaime Mejia,Unknown,False,feature
PATTERN-0452,Merged PR 2170 bug(TracksPage) Fix navigation bug in tracks view navigating to complaint...,ios_code,2023-03-14,2023-03-14,1,13,11,24,1,1,1.04,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0453,Merged PR 2169 bug(TracksPage) Fix navigation bug in tracks view navigating to complaint...,ios_code,2023-03-14,2023-03-14,1,13,11,24,1,1,1.04,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0454,bug(TracksPage) Fix navigation bug in tracks view navigating to complaints view,ios_code,2023-03-14,2023-03-14,1,13,11,24,1,1,1.04,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0455,Merged PR 2165 bug (ComplaintsTextInput2View) add more thant 3 digits,ios_code,2023-03-14,2023-03-14,1,28,5,33,3,1,2.13,1,Bryan Alexis García García,Unknown,False,feature
PATTERN-0456,Merged PR 2166 bug (ComplaintsTextInput2View) add more thant 3 digits,ios_code,2023-03-14,2023-03-14,1,28,5,33,3,1,2.13,1,Bryan Alexis García García,Unknown,False,feature
PATTERN-0457,bug (ComplaintsTextInput2View) add more thant 3 digits,ios_code,2023-03-14,2023-03-14,1,28,5,33,3,1,2.13,1,bryan garcia,Unknown,False,feature
PATTERN-0458,"Merged PR 2164 bug(LoginPage,MasterPage) Fix redirect to biometric login when user logout...",ios_code,2023-03-14,2023-03-14,1,15,1,16,2,1,1.46,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0459,"Merged PR 2163 bug(LoginPage,MasterPage) Fix redirect to biometric login when user logout...",ios_code,2023-03-14,2023-03-14,1,15,1,16,2,1,1.46,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0460,"bug(LoginPage,MasterPage) Fix redirect to biometric login when user logout first time after activated biometric login feature.",ios_code,2023-03-14,2023-03-14,1,15,1,16,2,1,1.46,1,Jaime Mejia,Unknown,False,feature
PATTERN-0461,Merged PR 2161 fix,project_config,2023-03-14,2023-03-14,1,31,37,68,2,1,1.98,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0462,Merged PR 2160 Fix,project_config,2023-03-14,2023-03-14,1,31,39,70,2,1,2.0,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0463,bug(GalleryLayoutView) Fix issue with video call,ios_code,2023-03-14,2023-03-14,1,11,17,28,1,1,1.08,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0464,Merged PR 2158 bug (BiometricService) alert menu has been updated,ios_code,2023-03-14,2023-03-14,1,4,2,6,2,1,1.36,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0465,Merged PR 2159 (BiometricService) alert menu has been updated,ios_code,2023-03-14,2023-03-14,1,4,2,6,2,1,1.36,1,Bryan Alexis García García,Unknown,False,maintenance
PATTERN-0466,bug Added sole view on first reset password,ios_code,2023-03-14,2023-03-14,1,39,19,58,2,1,1.88,1,bryan garcia,Unknown,False,feature
PATTERN-0467,"Merged PR 2130 bug(EndpointPublicIp,ApiManager) Fix error getting ip public in some endp...",ios_code,2023-03-10,2023-03-10,1,5,4,9,2,1,1.39,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0468,"bug(EndpointPublicIp,ApiManager) Fix error getting ip public in some endpoints.",ios_code,2023-03-10,2023-03-10,1,5,4,9,2,1,1.39,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0469,Merged PR 2126 Fix Error alert wasnt displayed,ios_code,2023-03-09,2023-03-09,1,12,4,16,3,1,1.96,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0470,"bug(LifeMilesViewModel,validateSelfieLifeMiles) Fix Error alert wasnt displayed",ios_code,2023-03-09,2023-03-09,1,10,1,11,2,1,1.41,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0471,Merged PR 2118 bug parametrizable text has been updated,ios_code,2023-03-09,2023-03-09,1,58,35,93,10,1,6.23,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0472,bug parametrizable text,ios_code,2023-03-08,2023-03-08,1,8,8,16,5,1,2.96,1,bryan garcia,Unknown,False,bugfix
PATTERN-0473,bug update version,project_config,2023-03-08,2023-03-08,1,10,10,20,1,1,1.0,1,bryan garcia,Unknown,False,bugfix
PATTERN-0474,bug parametrizable text has been updated,ios_code,2023-03-08,2023-03-08,1,42,19,61,6,1,3.91,1,bryan garcia,Unknown,False,bugfix
PATTERN-0475,bug reset password,ios_code,2023-03-08,2023-03-08,1,6,1,7,1,1,0.87,1,bryan garcia,Unknown,False,bugfix
PATTERN-0476,Merged PR 2113 bug fix vieModel,ios_code,2023-03-08,2023-03-08,1,41,41,82,1,1,1.62,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0477,bug fix vieModel,ios_code,2023-03-08,2023-03-08,1,41,41,82,1,1,1.62,1,bryan garcia,Unknown,False,bugfix
PATTERN-0478,bug biometria,ios_code,2023-03-07,2023-03-07,1,36,41,77,19,1,10.0,1,bryan garcia,Unknown,False,bugfix
PATTERN-0479,Merged PR 2105 bug (loginPage) isBusy,ios_code,2023-03-07,2023-03-07,1,4,3,7,2,1,1.37,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0480,bug (loginPage) isBusy,ios_code,2023-03-07,2023-03-07,1,4,3,7,2,1,1.37,1,bryan garcia,Unknown,False,bugfix
PATTERN-0481,Merged PR 2098 Remove unused files and structs,ios_code,2023-03-07,2023-03-07,1,330,5365,5695,165,1,10.0,1,Jaime Arévalo,Unknown,False,other
PATTERN-0482,Merged PR 2079 bug Fix bug in Protección plan,ios_code,2023-03-03,2023-03-03,1,76,120,196,19,1,10.0,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0483,bug Fix bug in Protección plan,ios_code,2023-03-03,2023-03-03,1,74,119,193,19,1,10.0,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0484,Merged PR 2070 bug(person-lines-fill.png) missing assets,other,2023-03-02,2023-03-02,1,3,7,10,7,1,3.9,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0485,bug(person-lines-fill.png) missing assets,other,2023-03-02,2023-03-02,1,3,7,10,7,1,3.9,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0486,"Merged PR 2068 bug(VerificarDatosUbicacionViewModel,UbiscacionMapaView) Fix bug about the...",ios_code,2023-03-02,2023-03-02,1,40,23,63,3,1,2.43,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0487,"bug(VerificarDatosUbicacionViewModel,UbiscacionMapaView) Fix bug about the pin location was using the current location instead the location ID",ios_code,2023-03-02,2023-03-02,1,39,21,60,2,1,1.9,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0488,Merged PR 2067 Fix bug about the database never was clean after send data to the server,ios_code,2023-03-02,2023-03-02,1,21,10,31,2,1,1.61,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0489,bug(FaltaPoco8ViewModel) Fix bug about the database never was clean after send data to the server,ios_code,2023-03-02,2023-03-02,1,21,8,29,2,1,1.59,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0490,Merged PR 2053 addresses issues has been resolved,ios_code,2023-03-01,2023-03-01,1,613,373,986,22,1,10.0,1,Bryan Alexis García García,Unknown,False,feature
PATTERN-0491,bug (travel Report) dates has been fixed,other,2023-03-01,2023-03-01,1,148,26,174,9,1,6.54,1,bryan garcia,Unknown,False,bugfix
PATTERN-0492,bug(UIissues) addresses issues has been resolved,ios_code,2023-03-01,2023-03-01,1,103,52,155,8,1,5.85,1,bryan garcia,Unknown,False,feature
PATTERN-0493,Merged PR 2051 bug (FaltaPoco3View) extra code,ios_code,2023-03-01,2023-03-01,1,10,11,21,2,1,1.51,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0494,bug (FaltaPoco3View) extra code,ios_code,2023-03-01,2023-03-01,1,10,11,21,2,1,1.51,1,bryan garcia,Unknown,False,bugfix
PATTERN-0495,bug (OTPPasoUno) refactor,ios_code,2023-03-01,2023-03-01,1,0,8,8,1,1,0.88,1,bryan garcia,Unknown,False,bugfix
PATTERN-0496,bug (RecordedVideo) has been fixed,ios_code,2023-02-28,2023-02-28,1,61,28,89,16,1,9.19,1,bryan garcia,Unknown,False,bugfix
PATTERN-0497,Merged PR 2045 bug(ConsultaDuiViewModel) alert was not showing error,ios_code,2023-02-28,2023-02-28,1,8,6,14,2,1,1.44,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0498,bug(ConsultaDuiViewModel) alert was not showing error,ios_code,2023-02-28,2023-02-28,1,8,6,14,2,1,1.44,1,bryan garcia,Unknown,False,bugfix
PATTERN-0499,bug(MasterPage) screenshot,ios_code,2023-02-27,2023-02-27,1,3,2,5,1,1,0.85,1,bryan garcia,Unknown,False,bugfix
PATTERN-0500,Merged PR 2037 bug (Onboarding) UI issues,ios_code,2023-02-26,2023-02-26,1,204,36,240,16,1,10.0,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0501,bug (Onboarding) UI issues,ios_code,2023-02-24,2023-02-24,1,15,2,17,7,1,3.97,1,bryan garcia,Unknown,False,bugfix
PATTERN-0502,Merged PR 2035 bug(AmplitudeIssues) fix server issues,ios_code,2023-02-24,2023-02-24,1,209,260,469,83,1,10.0,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0503,bug(AmplitudeIssues) fix server issues,ios_code,2023-02-24,2023-02-24,1,208,261,469,82,1,10.0,1,bryan garcia,Unknown,False,bugfix
PATTERN-0504,Merged PR 2032 bug(firstResetPassword) backs and flow Fixed,ios_code,2023-02-24,2023-02-24,1,86,77,163,14,1,8.93,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0505,bug(firstResetPassword) backs and flow Fixed,ios_code,2023-02-24,2023-02-24,1,86,77,163,14,1,8.93,1,bryan garcia,Unknown,False,bugfix
PATTERN-0506,Merged PR 2029 bug(AmplitudeTrackingmanager) Amplitude tracking manager added,ios_code,2023-02-24,2023-02-24,1,499,473,972,183,1,10.0,1,Bryan Alexis García García,Unknown,False,feature
PATTERN-0507,bug(AmplitudeTrackingmanager) Amplitude tracking manager added,ios_code,2023-02-24,2023-02-24,1,499,473,972,183,1,10.0,1,bryan garcia,Unknown,False,feature
PATTERN-0508,Merged PR 2028 bug(Around the App) back action fixed,ios_code,2023-02-24,2023-02-24,1,1,8,9,2,1,1.39,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0509,bug(Around the App) back action fixed,ios_code,2023-02-24,2023-02-24,1,1,8,9,2,1,1.39,1,bryan garcia,Unknown,False,bugfix
PATTERN-0510,Merged PR 2026 Backs issues,ios_code,2023-02-24,2023-02-24,1,1097,174,1271,63,1,10.0,1,Bryan Alexis García García,Unknown,False,other
PATTERN-0511,bug(Around the App) back action fixed,ios_code,2023-02-24,2023-02-24,1,3,7,10,1,1,0.9,1,bryan garcia,Unknown,False,bugfix
PATTERN-0512,bug(Around the App) back action fixed,ios_code,2023-02-24,2023-02-24,1,38,31,69,13,1,7.49,1,bryan garcia,Unknown,False,bugfix
PATTERN-0513,bug(Around the App) back action fixed,ios_code,2023-02-24,2023-02-24,1,40,75,115,14,1,8.45,1,bryan garcia,Unknown,False,bugfix
PATTERN-0514,bug(Around the App) back action fixed,ios_code,2023-02-24,2023-02-24,1,1097,149,1246,50,1,10.0,1,bryan garcia,Unknown,False,bugfix
PATTERN-0515,Merged PR 2017 bug(AuthService) firebasetoken issues,ios_code,2023-02-23,2023-02-23,1,27,93,120,5,1,4.0,1,Bryan Alexis García García,Unknown,False,bugfix
PATTERN-0516,bug(AuthService) firebasetoken issues,ios_code,2023-02-23,2023-02-23,1,27,93,120,5,1,4.0,1,bryan garcia,Unknown,False,bugfix
PATTERN-0517,Merged PR 2010 Backs issues and ui inside of the app,ios_code,2023-02-22,2023-02-22,1,21,12,33,10,1,5.63,1,Bryan Alexis García García,Unknown,False,other
PATTERN-0518,bug(backPayCard) back action fixed and login logic fixed,ios_code,2023-02-22,2023-02-22,1,12,6,18,5,1,2.98,1,bryan garcia,Unknown,False,bugfix
PATTERN-0519,bug(ComplaintSubMenuView) back action fixes,ios_code,2023-02-21,2023-02-21,1,8,8,16,3,1,1.96,1,bryan garcia,Unknown,False,bugfix
PATTERN-0520,bug(quotesPaymentView) ui scroll has been fixed,ios_code,2023-02-21,2023-02-21,1,8,5,13,4,1,2.43,1,bryan garcia,Unknown,False,bugfix
PATTERN-0521,bug (project.pbxproj) confict,project_config,2023-02-17,2023-02-17,1,27,0,27,1,1,1.07,1,bryan garcia,Unknown,False,bugfix
PATTERN-0522,Merged PR 1990 bug(SplashView) fix issue,ios_code,2023-02-16,2023-02-16,1,0,221,221,1,1,3.01,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0523,bug(SplashView) fix issue,ios_code,2023-02-16,2023-02-16,1,0,221,221,1,1,3.01,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0524,"bug (ComplaintsMenuButtonView,ComplaintsSubMenuButtonView) Add new import reference for amplitude",ios_code,2023-02-15,2023-02-15,1,3,5,8,3,1,1.88,1,Jaime Mejia,Unknown,False,feature
PATTERN-0525,refactor CardsMenu has been refactored,ios_code,2023-03-25,2023-03-25,1,1624,1064,2688,47,1,10.0,1,bryan garcia,Unknown,False,refactor
PATTERN-0526,refactor (DireccionesListView) has been updated,ios_code,2023-03-16,2023-03-16,1,65,5,70,4,1,3.0,1,bryan garcia,Unknown,False,refactor
PATTERN-0527,ONE-Github - Add github configurations (2),documentation,2025-09-24,2025-09-24,1,161,0,161,4,1,3.91,1,Emilio Vásquez,Unknown,False,feature
PATTERN-0528,Merged in featureONE-retriable (pull request 1000),source/Components,2025-06-18,2025-06-18,1,197,26,223,7,1,6.03,1,Julio Rico,Unknown,False,feature
PATTERN-0529,ONE-Components - Bullet list item component (pull request 987),source/Components,2025-06-09,2025-06-09,1,63,0,63,2,1,1.93,1,Julio Rico,Unknown,False,other
PATTERN-0530,Merged in release1.16.0 (pull request 906),ios_code,2025-04-11,2025-04-11,1,62556,59007,121563,2793,1,10.0,1,Emely Melgar,Unknown,False,testing
PATTERN-0531,Change variable name to avoid collision.,other,2023-10-31,2023-10-31,1,20,19,39,1,1,1.19,1,Mauricio Jovel,Unknown,False,other
PATTERN-0532,Add new method to make the build.,ci_cd,2023-10-30,2023-10-30,1,16,9,25,2,1,1.55,1,Mauricio Jovel,Unknown,False,feature
PATTERN-0533,Merged in release1.5.0 (pull request 181),ios_code,2023-11-14,2023-11-14,1,9727,4744,14471,277,1,10.0,1,Emely Melgar,Unknown,False,testing
PATTERN-0534,Sync release 1.1.0 (9) with develop (pull request 23),project_config,2023-07-21,2023-07-21,1,5,5,10,1,1,0.9,1,Emely Melgar,Unknown,False,other
PATTERN-0535,TLNTD - Bump build number to 9,project_config,2023-07-17,2023-07-17,1,4,4,8,1,1,0.88,1,jblanco-applaudostudios,Unknown,False,other
PATTERN-0536,TLNTD - Bump build number,project_config,2023-07-17,2023-07-17,1,31,31,62,1,1,1.42,1,jblanco-applaudostudios,Unknown,False,other
PATTERN-0537,Sync release 1.1.0 (8) with develop,other,2023-06-30,2023-06-30,1,243,140,383,4,1,6.13,1,Josseh Blanco,Unknown,False,other
PATTERN-0538,ONE - 3138v1 - move filter to function appart,source/Modules,2025-09-18,2025-09-18,1,6,2,8,1,1,0.88,1,Javier Lorenzana,Unknown,False,other
PATTERN-0539,ONE-SignatureContract OB - Add missing back condition on OB navigation (pull request 1045),source/Components,2025-07-10,2025-07-10,1,5,3,8,3,1,1.88,1,Edgar Emilio Vásquez Castillo,Unknown,False,feature
PATTERN-0540,ONE-BackOnboardingSteps - Apply changes to Signature contract in order to support avoid back on onboarding (pull request 1041),source/Components,2025-07-09,2025-07-09,1,32,10,42,12,1,6.72,1,Edgar Emilio Vásquez Castillo,Unknown,False,other
PATTERN-0541,ONE-FixLoaderIdentity - Fix loader behavior (pull request 999),source/Components,2025-06-18,2025-06-18,1,17,4,21,3,1,2.01,1,Edgar Emilio Vásquez Castillo,Unknown,False,bugfix
PATTERN-0542,ONE - Fix OneTextField and OneSelectableView edge cases (pull request 966),source/Components,2025-05-28,2025-05-28,1,12,5,17,3,1,1.97,1,Josseh Blanco,Unknown,False,bugfix
PATTERN-0543,Merged in release1.16.0 (pull request 906),ios_code,2025-04-11,2025-04-11,1,62556,59007,121563,2793,1,10.0,1,Emely Melgar,Unknown,False,testing
PATTERN-0544,ONEAPP - Log onboarding events only if the app tracking permission is enabled (pull request 452),source/Core,2024-04-30,2024-04-30,1,30,3,33,3,1,2.13,1,Josseh Blanco,Unknown,False,other
PATTERN-0545,ONEAP-Fix-AdditionalCard (pull request 312),source/Modules,2024-02-26,2024-02-26,1,13,15,28,3,1,2.08,1,Edgar Emilio Vásquez Castillo,Unknown,False,feature
PATTERN-0546,Merged in release1.9.0 (pull request 301),ios_code,2024-02-12,2024-02-12,1,137,69,206,19,1,10.0,1,Emely Melgar,Unknown,False,other
PATTERN-0547,"Merged PR 2384 (SplashView,SplashViewModel) Fix issue in force update app",ios_code,2023-04-20,2023-04-20,1,47,38,85,4,1,3.15,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0548,"(SplashView,SplashViewModel) Fix issue in force update app",ios_code,2023-04-20,2023-04-20,1,47,38,85,4,1,3.15,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0549,Merged PR 2359 ( ChatView) Fix issue when the chat was started and fix anonymous identity.,ios_code,2023-04-14,2023-04-14,1,230,200,430,4,1,6.6,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0550,Merged PR 2360 ( ChatView) Fix issue when the chat was started and fix anonymous identity.,ios_code,2023-04-14,2023-04-14,1,171,142,313,3,1,4.93,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0551,( ChatView) Fix issue when the chat was started and fix anonymous identity.,ios_code,2023-04-14,2023-04-14,1,171,142,313,3,1,4.93,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0552,( ChatView) Fix issue when the chat was started and fix anonymous identity.,ios_code,2023-04-14,2023-04-14,1,230,200,430,4,1,6.6,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0553,Merged PR 2356 (ChatView) Fix twilio chat with colors of bubble,ios_code,2023-04-14,2023-04-14,1,24,70,94,1,1,1.74,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0554,(ChatView) Fix issue with the space in the bottom when keyboard is shown,ios_code,2023-04-13,2023-04-13,1,117,106,223,6,1,5.53,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0555,(ChatView) Fix twilio chat with colors of bubble,ios_code,2023-04-13,2023-04-13,1,24,70,94,1,1,1.74,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0556,Merged PR 665 Fix UI issues.,other,2022-03-23,2022-03-23,1,172,124,296,51,1,10.0,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0557,UI issues.,other,2022-03-22,2022-03-22,1,172,124,296,51,1,10.0,1,Jaime Mejia,Unknown,False,other
PATTERN-0558,Merged PR 653 Fix date format in trusted devices view and I added latitude and longitude t...,ios_code,2022-03-19,2022-03-19,1,40,4,44,4,1,2.74,1,Jaime Arévalo,Unknown,False,feature
PATTERN-0559,date format in trusted devices view and I added latitude and longitude to login service.,ios_code,2022-03-18,2022-03-18,1,40,4,44,4,1,2.74,1,Jaime Mejia,Unknown,False,feature
PATTERN-0560,Merged PR 651 Fix Color status,ios_code,2022-03-18,2022-03-18,1,5,5,10,2,1,1.4,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0561,Color status,ios_code,2022-03-18,2022-03-18,1,5,5,10,2,1,1.4,1,Jaime Mejia,Unknown,False,other
PATTERN-0562,"Format View, format date, status text color, format text",ios_code,2022-03-18,2022-03-18,1,22,6,28,2,1,1.58,1,Jaime Mejia,Unknown,False,other
PATTERN-0563,MapView crashed,ios_code,2022-03-18,2022-03-18,1,7,4,11,2,1,1.41,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0564,Merged PR 635 Fix Additional card work flow,ios_code,2022-03-17,2022-03-17,1,537,305,842,32,1,10.0,1,Jaime Arévalo,Unknown,False,feature
PATTERN-0565,Additional card work flow Fix Back button in map view New dropdown component added,ios_code,2022-03-16,2022-03-16,1,537,305,842,32,1,10.0,1,Jaime Mejia,Unknown,False,feature
PATTERN-0566,Merged PR 621 FixBug2901,ios_code,2022-03-15,2022-03-15,1,11,3,14,3,1,1.94,1,Jaime Arévalo,Unknown,False,bugfix
PATTERN-0567,Bug2901,ios_code,2022-03-14,2022-03-14,1,11,3,14,3,1,1.94,1,Jaime Mejia,Unknown,False,bugfix
PATTERN-0568,I create new component dropdown just for dynamics forms,ios_code,2022-03-14,2022-03-14,1,348,2,350,3,1,5.3,1,Jaime Mejia,Unknown,False,feature
PATTERN-0569,variable BASEURL was change to computed var depending of value of QAMode user default,ios_code,2022-03-08,2022-03-08,1,13,2,15,2,1,1.45,1,Jaime Mejia,Unknown,False,other
PATTERN-0570,Multiple Controls of the same type in a single view. Fix Cancel option Fix Validation of controls Fix missed form structure when the service of complaints response code 200 add Tracking view add Red v,ios_code,2022-03-03,2022-03-03,1,876,79,955,20,1,10.0,1,Jaime Mejia,Unknown,False,feature
PATTERN-0571,DropdownView,ios_code,2022-03-03,2022-03-03,1,62,7,69,4,1,2.99,1,Jaime Mejia,Unknown,False,other
SP-2421,ONE-_- Fix the logic to request transactions based on the selected state. (pull request 1054),source/Modules,2025-07-17,2025-07-17,1,49,33,82,3,1,2.62,1,Emely Melgar,Unknown,True,bugfix
SUBSTANTIAL-0001,ONE - Include OnePageIndicator and dynamicHeight optimization.,source/Components,2025-09-30,2025-09-30,1,45,17,62,3,1,2.42,1,Emely Melgar,Unknown,False,other
SUBSTANTIAL-0002,GitHub reviewers - Add BAES team default reviewers (7),documentation,2025-09-25,2025-09-25,1,37,15,52,2,1,1.82,1,Emilio Vásquez,Unknown,False,feature
SUBSTANTIAL-0003,Version bumped by fastlane. skip ci,configuration,2025-08-15,2025-08-15,1,26,26,52,2,1,1.82,1,Jenkins CI,Unknown,False,maintenance
SUBSTANTIAL-0004,Version bumped by fastlane. skip ci,configuration,2025-08-13,2025-08-13,1,26,26,52,2,1,1.82,1,Jenkins CI,Unknown,False,maintenance
SUBSTANTIAL-0005,ONE-OTPView-Improvement - Improve OTP View UI and Performance (pull request 1065),source/Modules,2025-07-24,2025-07-24,1,37,36,73,5,1,3.53,1,Edgar Emilio Vásquez Castillo,Unknown,False,other
SUBSTANTIAL-0006,ONE-CI - Comment failing tests (pull request 1024),tests,2025-07-03,2025-07-03,1,83,82,165,1,1,2.45,1,Josseh Blanco,Unknown,False,testing
SUBSTANTIAL-0007,ONEAPP-Document preview updated (pull request 957),source/Modules,2025-05-23,2025-05-23,1,10,9,19,6,1,3.49,1,José De la O,Unknown,False,maintenance
SUBSTANTIAL-0008,ONE-CI - Include Azure pipelines. (pull request 948),other,2025-05-20,2025-05-20,1,114,720,834,3,1,10.0,1,Alexander Sosa,Unknown,False,other
SUBSTANTIAL-0009,ONE-CI - Include Azure pipelines. (pull request 948),other,2025-05-20,2025-05-20,1,114,751,865,3,1,10.0,1,Alexander Sosa,Unknown,False,other
SUBSTANTIAL-0010,ONE-CI - Include Azure pipelines. (pull request 948),other,2025-05-20,2025-05-20,1,114,747,861,3,1,10.0,1,Alexander Sosa,Unknown,False,other
SUBSTANTIAL-0011,ONE-CI - Include Azure pipelines. (pull request 948),other,2025-05-20,2025-05-20,1,114,716,830,3,1,10.0,1,Alexander Sosa,Unknown,False,other
SUBSTANTIAL-0012,ONEAPP - Add support for facephi in simulator builds,source/Modules,2025-05-15,2025-05-15,1,56,1,57,6,1,3.87,1,Josseh Blanco,Unknown,False,feature
SUBSTANTIAL-0013,Set up CI with Azure Pipelines,documentation,2025-05-13,2025-05-13,1,102,0,102,1,1,1.82,1,Alexander Sosa,Unknown,False,other
SUBSTANTIAL-0014,ONEAPP-- New Selphi and SelphiID build updated.,ios_code,2025-03-24,2025-03-24,1,61816,58879,120695,2764,1,10.0,1,José De la O,Unknown,False,feature
SUBSTANTIAL-0015,ONE-ServicesPayment-Sync (pull request 838),resources,2025-02-20,2025-02-20,1,346,29831,30177,213,1,10.0,1,Edgar Emilio Vásquez Castillo,Unknown,False,testing
SUBSTANTIAL-0016,Updates Podfile and prepares otp files for future refactoring (pull request 742),source/{Modules,2024-12-04,2024-12-04,1,50,41,91,17,1,9.71,1,Rodrigo Mejia,Unknown,False,refactor
SUBSTANTIAL-0017,ONE-- New endpoint IdentificationImageAdd implemented (pull request 724),source/Core,2024-11-27,2024-11-27,1,259,28,287,9,1,7.67,1,José De la O,Unknown,False,feature
SUBSTANTIAL-0018,- Crate CreditCardOfferDetailsVew component,source/Modules,2024-10-16,2024-10-16,1,863,549,1412,35,1,10.0,1,Vladimir Guevara,Unknown,False,other
